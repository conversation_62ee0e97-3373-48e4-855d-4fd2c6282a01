fileFormatVersion: 2
guid: 5e331400f64621b439dbed5d1a172aac
ModelImporter:
  serializedVersion: 21300
  internalIDToNameTable:
  - first:
      1: 100000
    second: A
  - first:
      1: 100002
    second: B
  - first:
      1: 100004
    second: BFlat
  - first:
      1: 100006
    second: C
  - first:
      1: 100008
    second: CSharp
  - first:
      1: 100010
    second: D
  - first:
      1: 100012
    second: E
  - first:
      1: 100014
    second: EFlat
  - first:
      1: 100016
    second: F
  - first:
      1: 100018
    second: FSharp
  - first:
      1: 100020
    second: G
  - first:
      1: 100022
    second: GSharp
  - first:
      1: 100024
    second: //RootNode
  - first:
      1: 100026
    second: Velvet
  - first:
      4: 400000
    second: A
  - first:
      4: 400002
    second: B
  - first:
      4: 400004
    second: BFlat
  - first:
      4: 400006
    second: C
  - first:
      4: 400008
    second: CSharp
  - first:
      4: 400010
    second: D
  - first:
      4: 400012
    second: E
  - first:
      4: 400014
    second: EFlat
  - first:
      4: 400016
    second: F
  - first:
      4: 400018
    second: FSharp
  - first:
      4: 400020
    second: G
  - first:
      4: 400022
    second: GSharp
  - first:
      4: 400024
    second: //RootNode
  - first:
      4: 400026
    second: Velvet
  - first:
      21: 2100000
    second: WhiteKeys
  - first:
      21: 2100002
    second: No Name
  - first:
      21: 2100004
    second: BlackKeys
  - first:
      21: 2100006
    second: Velvet
  - first:
      21: 2100008
    second: Default_Material
  - first:
      21: 2100010
    second: Velvet1
  - first:
      23: 2300000
    second: A
  - first:
      23: 2300002
    second: B
  - first:
      23: 2300004
    second: BFlat
  - first:
      23: 2300006
    second: C
  - first:
      23: 2300008
    second: CSharp
  - first:
      23: 2300010
    second: D
  - first:
      23: 2300012
    second: E
  - first:
      23: 2300014
    second: EFlat
  - first:
      23: 2300016
    second: F
  - first:
      23: 2300018
    second: FSharp
  - first:
      23: 2300020
    second: G
  - first:
      23: 2300022
    second: GSharp
  - first:
      23: 2300024
    second: Velvet
  - first:
      33: 3300000
    second: A
  - first:
      33: 3300002
    second: B
  - first:
      33: 3300004
    second: BFlat
  - first:
      33: 3300006
    second: C
  - first:
      33: 3300008
    second: CSharp
  - first:
      33: 3300010
    second: D
  - first:
      33: 3300012
    second: E
  - first:
      33: 3300014
    second: EFlat
  - first:
      33: 3300016
    second: F
  - first:
      33: 3300018
    second: FSharp
  - first:
      33: 3300020
    second: G
  - first:
      33: 3300022
    second: GSharp
  - first:
      33: 3300024
    second: Velvet
  - first:
      43: 4300000
    second: C
  - first:
      43: 4300002
    second: CSharp
  - first:
      43: 4300004
    second: D
  - first:
      43: 4300006
    second: EFlat
  - first:
      43: 4300008
    second: E
  - first:
      43: 4300010
    second: F
  - first:
      43: 4300012
    second: FSharp
  - first:
      43: 4300014
    second: G
  - first:
      43: 4300016
    second: GSharp
  - first:
      43: 4300018
    second: A
  - first:
      43: 4300020
    second: BFlat
  - first:
      43: 4300022
    second: B
  - first:
      43: 4300024
    second: Velvet
  - first:
      41386430: 2186277476908879412
    second: ImportLogs
  externalObjects:
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: BlackKeys
    second: {fileID: 2100000, guid: 987f7ade5b34fb74dbefd305bbbf85cf, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: No Name
    second: {fileID: 2100000, guid: 680505b4b4f3400f9fd3ebb242751819, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Velvet
    second: {fileID: 2100000, guid: c4a1b7475a654dd0acaa0cfdfba2e20c, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Velvet1
    second: {fileID: 2100000, guid: db61b94b23e5fb444b86c231d13e46ef, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: WhiteKeys
    second: {fileID: 2100000, guid: f62f188dc6a5f55458ac47e93b3951f4, type: 2}
  materials:
    materialImportMode: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    nodeNameCollisionStrategy: 0
    fileIdsGeneration: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 1
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 0
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 1
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 0
  humanoidOversampling: 1
  avatarSetup: 0
  addHumanoidExtraRootOnlyWhenUsingAvatar: 0
  remapMaterialsIfMaterialImportModeIsNone: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
