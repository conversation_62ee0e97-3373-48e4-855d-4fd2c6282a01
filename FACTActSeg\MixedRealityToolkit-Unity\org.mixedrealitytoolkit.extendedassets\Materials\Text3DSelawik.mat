%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Text3DSelawik
  m_Shader: {fileID: 4800000, guid: 80c006b91733f1a4991c49af89321ecd, type: 3}
  m_ValidKeywords: []
  m_InvalidKeywords:
  - _BORDER_LIGHT_USES_HOVER_COLOR
  - _DIRECTIONAL_LIGHT
  - _HOVER_LIGHT
  - _SPECULAR_HIGHLIGHTS
  m_LightmapFlags: 5
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ChannelMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: 2d0b36401e6edcd4ea4a8157de49d41b, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlbedoAlphaMode: 0
    - _BlendOp: 0
    - _BorderLight: 0
    - _BorderLightOpaque: 0
    - _BorderLightUsesHoverColor: 1
    - _BorderMinValue: 0.1
    - _BorderWidth: 0.1
    - _BumpScale: 1
    - _ClippingPlane: 0
    - _ClippingPlaneBorder: 0
    - _ClippingPlaneBorderWidth: 0.025
    - _ColorMask: 15
    - _ColorWriteMask: 15
    - _Cull: 0
    - _CullMode: 2
    - _CustomMode: 0
    - _Cutoff: 0.5
    - _DetailNormalMapScale: 1
    - _DirectionalLight: 1
    - _DstBlend: 0
    - _EdgeSmoothingValue: 0.002
    - _EnableChannelMap: 0
    - _EnableEmission: 0
    - _EnableHoverColorOpaqueOverride: 0
    - _EnableHoverColorOverride: 0
    - _EnableNormalMap: 0
    - _EnvironmentColorIntensity: 0.5
    - _EnvironmentColorThreshold: 1.5
    - _EnvironmentColoring: 0
    - _FadeBeginDistance: 0.85
    - _FadeCompleteDistance: 0.5
    - _Glossiness: 0.5
    - _HoverLight: 1
    - _HoverLightOpaque: 0
    - _InnerGlow: 0
    - _InstancedColor: 0
    - _Metallic: 0
    - _Mode: 0
    - _NearPlaneFade: 0
    - _OcclusionStrength: 1
    - _Parallax: 0.02
    - _Reflections: 0
    - _Refraction: 0
    - _RefractiveIndex: 0
    - _RenderQueueOverride: -1
    - _RimLight: 0
    - _RimPower: 0.25
    - _RoundCornerMargin: 0
    - _RoundCornerRadius: 0.25
    - _RoundCorners: 0
    - _Smoothness: 0.5
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _Stencil: 0
    - _StencilComp: 8
    - _StencilComparison: 0
    - _StencilOp: 0
    - _StencilOperation: 0
    - _StencilReadMask: 255
    - _StencilReference: 0
    - _StencilWriteMask: 255
    - _UVSec: 0
    - _UseUIAlphaClip: 0
    - _ZTest: 4
    - _ZWrite: 1
    m_Colors:
    - _ClipPlane: {r: 0, g: 1, b: 0, a: 0}
    - _ClippingPlaneBorderColor: {r: 1, g: 0.2, b: 0, a: 1}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _EmissiveColor: {r: 0, g: 0, b: 0, a: 1}
    - _EnvironmentColorX: {r: 1, g: 0, b: 0, a: 1}
    - _EnvironmentColorY: {r: 0, g: 1, b: 0, a: 1}
    - _EnvironmentColorZ: {r: 0, g: 0, b: 1, a: 1}
    - _HoverColorOpaqueOverride: {r: 1, g: 1, b: 1, a: 1}
    - _HoverColorOverride: {r: 1, g: 1, b: 1, a: 1}
    - _InnerGlowColor: {r: 1, g: 1, b: 1, a: 0.75}
    - _RimColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_BuildTextureStacks: []
