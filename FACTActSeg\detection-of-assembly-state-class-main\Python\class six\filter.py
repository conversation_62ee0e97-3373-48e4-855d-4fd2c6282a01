from ultralytics.engine.results import Results
from typing import Union
import math
import json
import cv2

class BaseFilterResult(object):
    def __init__(self, warning=False, wrong=False, missing=False):
        super().__init__()
        self.warning = warning
        self.wrong = wrong
        self.missing= missing


class YOLOFilterResult(BaseFilterResult):
    def __init__(self, img, class_names, warning=False, boxes=None, wrong=False, missing=False):
        super().__init__(warning, wrong, missing)
        if boxes is None:
            boxes = []
        self.boxes = boxes
        self.img = img
        self.class_names = class_names


class OCRResult(object):
    def __init__(self, img, text_list: list):
        self.img = img
        self.text_list = text_list


class OCRFilterResult(BaseFilterResult):
    def __init__(self, img, warning=False, wrong=False, missing=False, texts=None):
        super().__init__(warning, wrong, missing)
        if texts is None:
            texts = []
        self.texts = texts
        self.img = img

class YOLOFilter(object):
    def __init__(self, conf):
        super().__init__()
        self.wrong_frame_num = 0
        self.__load_conf__(conf)

    def reset(self):
        self.wrong_frame_num = 0

    def reload(self, config_name):
        self.reset()
        self.__load_conf__(config_name)

    def adsorb_angle(self, angle):
        return min(self.angles.keys(), key=lambda x: abs(x-angle))

    def __call__(self, result: Results) -> YOLOFilterResult:

        is_wrong = False
        warning = False
        wrong = False
        missing = False

        xy_array = []

        # 分别处理形状错漏装与位置错漏装
        if self.type == "position":
            if float(self.target) in result.boxes.cls and len(result.boxes.cls) > 1:
                # find center obj
                index = result.boxes.cls.tolist().index(float(self.target))
                center = result.boxes[index]
                xyxy = center.xyxy  # .cpu().numpy()
                xy_array.append(
                    [(int(xyxy[0][0]), int(xyxy[0][1])), (int(xyxy[0][2]), int(xyxy[0][3])), int(center.cls)]
                )

                # init obj list
                direction_obj = {}
                for key in self.directions.keys():
                    direction_obj[key] = []

                # calculate side objects
                side_obj = []
                for box, i in zip(result.boxes, range(len(result.boxes))):
                    if box.conf > self.CONFIDENCE and i != index:
                        xyxy = box.xyxy  # .cpu().numpy()
                        xy_array.append(
                            [(int(xyxy[0][0]), int(xyxy[0][1])), (int(xyxy[0][2]), int(xyxy[0][3])), int(box.cls)]
                        )
                        # (distance,angle,cls)三元组
                        distance = abs(box.xywhn[0][0]-center.xywhn[0][0])+abs(box.xywhn[0][1]-center.xywhn[0][1])
                        # y axis reserve
                        angel = math.degrees(math.atan2(center.xywhn[0][1] - box.xywhn[0][1],
                                                        box.xywhn[0][0] - center.xywhn[0][0])
                                             )
                        side_obj.append((distance, self.adsorb_angle(angel), int(box.cls)))

                # calculate side obj array
                side_obj.sort(key=lambda x: x[0])
                for each in side_obj:
                    direction_obj[self.angles[each[1]]].append(each[2])

                # check equal
                for direction in self.directions.keys():
                    if direction_obj[direction] != self.directions[direction]["objects"]:
                        is_wrong = True
                        wrong = True
                        self.wrong_frame_num += 1
                        break
            else:
                missing = True
                is_wrong = True
                self.wrong_frame_num += 1

        elif self.type == "shape":
            # box check
            if len(result.boxes) == 0:
                missing = True
                is_wrong = True
                self.wrong_frame_num += 1
            for box in result.boxes:
                # print(wrong_frame_num)

                if box.conf > self.CONFIDENCE and 1 << int(box.cls) & self.wrong_code:
                    is_wrong = True
                    wrong = True
                    self.wrong_frame_num += 1

                    # get x,y
                    xyxy = box.xyxy  # .cpu().numpy()

                    xy_array.append([(int(xyxy[0][0]), int(xyxy[0][1])), (int(xyxy[0][2]), int(xyxy[0][3])),int(box.cls)])
                    break

        # decrease
        if not is_wrong:
            self.wrong_frame_num //= 10
            self.wrong_frame_num *= self.factor

        # draw warning
        if self.wrong_frame_num >= self.WRONG_FRAME_TOTAL:
            self.wrong_frame_num = self.WRONG_FRAME_TOTAL
            warning = True

        return YOLOFilterResult(result.orig_img, self.class_names, warning, xy_array, wrong, missing)

    def __load_conf__(self, name: str):
        with open(name, "r") as f:
            conf = json.load(f)
            self.type = conf["TYPE"]
            if conf["TYPE"] == "position":
                self.WRONG_FRAME_TOTAL = conf["WRONG_ALLOW"]
                self.CONFIDENCE = conf["CONFIDENCE"]
                self.target = conf["TARGET"]
                self.directions = conf["DIRECTIONS"]
                self.class_names = conf["NAME"]
                self.factor = conf["DECREASE_FACTOR"]
                # angle -> direction
                self.angles = {}
                for each in self.directions.items():
                    self.angles[each[1]["angle"]] = each[0]
            elif conf["TYPE"] == "shape":
                self.WRONG_FRAME_TOTAL = conf["WRONG_ALLOW"]
                self.CONFIDENCE = conf["CONFIDENCE"]
                self.wrong_code = conf["WRONG_CODE"]
                self.class_names = conf["NAME"]
                self.factor = conf["DECREASE_FACTOR"]