import json
import os
import albumentations as A
import cv2
import numpy as np

# 数据增强
def trans(img_path, box_path, num):
    transformer = <PERSON><PERSON>([
        A.FancyPCA(p=0.5),
        <PERSON><PERSON>([<PERSON><PERSON>(p=0.5),
                 <PERSON><PERSON>(p=0.5),
                 <PERSON><PERSON>(p=0.5)
                 ], p=0.8),
        <PERSON><PERSON>(p=0.5),
        A.OneOf([
            A.Cutout(num_holes=5, max_h_size=64, max_w_size=64, p=0.5),
            <PERSON><PERSON>BBoxSafeRandomCrop(p=0.3)
        ], p=0.5),
        <PERSON><PERSON>(rotate_limit=10, p=0.8, border_mode=cv2.BORDER_CONSTANT, value=0),
        A.RandomBrightness<PERSON>ontrast(p=0.1),
    ], bbox_params=A.BboxParams(format='yolo', min_visibility=0.2))
    boxes = []

    with open(box_path,"r") as f:
        for line in f.readlines():
            box = line.split(" ")[1:]
            boxes.append([float(each) for each in box] + [line.split(" ")[0]])
    img = cv2.cvtColor(cv2.imread(img_path), cv2.COLOR_BGR2RGB)

    for i in range(num):
        result = transformer(image=img, bboxes=boxes)
        cv2.imwrite(img_path.replace(".",f"_{i}."), cv2.cvtColor(result["image"],cv2.COLOR_RGB2BGR),)
        with open(box_path.replace(".",f"_{i}."),"w") as f:
            for j in range(len(result["bboxes"])):
                bboxes = [result["bboxes"][j][-1]] + [str(each) for each in result["bboxes"][j][:-1]]
                f.write(" ".join(bboxes)+"\n")

if __name__ == "__main__":
    img_dir = "images"
    bbox_dir = "labels"
    img_names = os.listdir(img_dir) #[each for each in os.listdir(img_dir) if "right" in each]
    box_names = os.listdir(bbox_dir) #[each for each in os.listdir(bbox_dir) if "right" in each]
    total = len(img_names)
    i = 0
    for img_name,box_name in zip(img_names, box_names):
        print(f"{i}/total", end="\r")
        trans(os.path.join(img_dir, img_name), os.path.join(bbox_dir, box_name), 10)