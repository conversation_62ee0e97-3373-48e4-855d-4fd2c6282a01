{"name": "MixedReality.Toolkit.Core", "rootNamespace": "MixedReality.Toolk<PERSON>", "references": ["Unity.XR.CoreUtils", "Unity.XR.Interaction.Toolkit", "Unity.XR.Management"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [{"name": "Unity", "expression": "2021.3.18", "define": "UNITY_2021_3_18_OR_NEWER"}], "noEngineReferences": false}