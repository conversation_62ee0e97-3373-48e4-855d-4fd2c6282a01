{"name": "org.mixedrealitytoolkit.core", "version": "3.2.3-development", "description": "A limited collection of common interfaces and utilities that most MRTK packages share. Most implementations of these interfaces are contained in other packages in the MRTK ecosystem.", "displayName": "MRTK Core Definitions", "msftFeatureCategory": "MRTK3", "author": "Mixed Reality Toolkit Contributors", "license": "BSD 3-<PERSON><PERSON>", "repository": {"type": "git", "url": "https://github.com/MixedRealityToolkit/MixedRealityToolkit-Unity.git"}, "bugs": {"url": "https://github.com/MixedRealityToolkit/MixedRealityToolkit-Unity/issues"}, "unity": "2021.3", "unityRelease": "26f1", "documentationUrl": "https://www.mixedrealitytoolkit.org", "dependencies": {"com.unity.xr.interaction.toolkit": "2.3.0", "com.unity.xr.management": "4.2.1", "com.unity.xr.core-utils": "2.1.0"}}