// Copyright (c) Mixed Reality Toolkit Contributors
// Licensed under the BSD 3-Clause

namespace MixedReality.Toolkit
{
    /// <summary>
    /// Used to specify a 3D axis.
    /// </summary>
    public enum Axis
    {
        /// <summary>
        /// The horizontal axis.
        /// </summary>
        X = 0,

        /// <summary>
        /// The vertical axis.
        /// </summary>
        Y = 1,

        /// <summary>
        /// The depth axis.
        /// </summary>
        Z = 2,
    }
}
