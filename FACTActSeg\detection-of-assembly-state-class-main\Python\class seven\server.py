import socket
import struct

if __name__ == '__main__':
    server = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    packer = struct.Struct("I")
    server.bind(("127.0.0.1", 9090))
    server.listen(5)
    conn, addr = server.accept()
    while True:
        send_data = input("input something >>>").encode("utf-8")
        send_data_len = len(send_data)
        conn.send(packer.pack(send_data_len))
        conn.send(send_data)
        recv_data_len = packer.unpack(conn.recv(4))[0]
        recv_data = conn.recv(recv_data_len).decode("utf-8")
        print("recv_data: ", recv_data)
