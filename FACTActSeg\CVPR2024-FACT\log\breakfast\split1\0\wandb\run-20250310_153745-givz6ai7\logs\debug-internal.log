2025-03-10 15:37:45,080 INFO    StreamThr :6744 [internal.py:wandb_internal():86] W&B internal server running at pid: 6744, started at: 2025-03-10 15:37:45.079405
2025-03-10 15:37:45,081 DEBUG   HandlerThread:6744 [handler.py:handle_request():146] handle_request: status
2025-03-10 15:37:45,085 INFO    WriterThread:6744 [datastore.py:open_for_write():87] open: log/breakfast\split1\0\wandb\run-20250310_153745-givz6ai7\run-givz6ai7.wandb
2025-03-10 15:37:45,086 DEBUG   SenderThread:6744 [sender.py:send():382] send: header
2025-03-10 15:37:45,115 DEBUG   SenderThread:6744 [sender.py:send():382] send: run
2025-03-10 15:37:45,123 INFO    SenderThread:6744 [sender.py:_maybe_setup_resume():763] checking resume status for zimingwang945/FACT-ActSeg/givz6ai7
2025-03-10 15:37:46,093 ERROR   SenderThread:6744 [internal_api.py:execute():373] 403 response executing GraphQL.
2025-03-10 15:37:46,093 ERROR   SenderThread:6744 [internal_api.py:execute():374] {"errors":[{"message":"permission denied","path":["upsertBucket"],"extensions":{"code":"PERMISSION_ERROR"}}],"data":{"upsertBucket":null}}
2025-03-10 15:37:46,095 ERROR   SenderThread:6744 [sender.py:send_run():987] It appears that you do not have permission to access the requested resource. Please reach out to the project owner to grant you access. If you have the correct permissions, verify that there are no issues with your networking setup.(Error 403: Forbidden)
Traceback (most recent call last):
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\sdk\lib\retry.py", line 131, in __call__
    result = self._call_fn(*args, **kwargs)
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\sdk\internal\internal_api.py", line 369, in execute
    return self.client.execute(*args, **kwargs)  # type: ignore
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\vendor\gql-0.2.0\wandb_gql\client.py", line 52, in execute
    result = self._get_result(document, *args, **kwargs)
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\vendor\gql-0.2.0\wandb_gql\client.py", line 60, in _get_result
    return self.transport.execute(document, *args, **kwargs)
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\sdk\lib\gql_request.py", line 59, in execute
    request.raise_for_status()
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\requests\models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 403 Client Error: Forbidden for url: https://api.wandb.ai/graphql

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\sdk\internal\sender.py", line 985, in send_run
    self._init_run(run, config_value_dict)
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\sdk\internal\sender.py", line 1027, in _init_run
    server_run, inserted, server_messages = self._api.upsert_run(
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\apis\normalize.py", line 73, in wrapper
    raise err
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\apis\normalize.py", line 41, in wrapper
    return func(*args, **kwargs)
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\sdk\internal\internal_api.py", line 2216, in upsert_run
    response = self.gql(
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\sdk\internal\internal_api.py", line 341, in gql
    ret = self._retry_gql(
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\sdk\lib\retry.py", line 147, in __call__
    retry_timedelta_triggered = check_retry_fn(e)
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\util.py", line 965, in check_retry_fn
    return fallback_retry_fn(e)
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\util.py", line 907, in no_retry_auth
    raise CommError(
wandb.errors.CommError: It appears that you do not have permission to access the requested resource. Please reach out to the project owner to grant you access. If you have the correct permissions, verify that there are no issues with your networking setup.(Error 403: Forbidden)
2025-03-10 15:37:46,795 DEBUG   HandlerThread:6744 [handler.py:handle_request():146] handle_request: shutdown
2025-03-10 15:37:46,795 INFO    HandlerThread:6744 [handler.py:finish():869] shutting down handler
2025-03-10 15:37:47,101 INFO    SenderThread:6744 [sender.py:finish():1572] shutting down sender
2025-03-10 15:37:47,132 INFO    WriterThread:6744 [datastore.py:close():296] close: log/breakfast\split1\0\wandb\run-20250310_153745-givz6ai7\run-givz6ai7.wandb
