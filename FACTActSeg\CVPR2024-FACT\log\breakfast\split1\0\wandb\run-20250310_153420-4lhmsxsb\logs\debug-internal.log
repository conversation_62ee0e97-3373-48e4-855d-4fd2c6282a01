2025-03-10 15:34:20,412 INFO    StreamThr :19076 [internal.py:wandb_internal():86] W&B internal server running at pid: 19076, started at: 2025-03-10 15:34:20.412528
2025-03-10 15:34:20,414 DEBUG   HandlerThread:19076 [handler.py:handle_request():146] handle_request: status
2025-03-10 15:34:20,414 INFO    WriterThread:19076 [datastore.py:open_for_write():87] open: log/breakfast\split1\0\wandb\run-20250310_153420-4lhmsxsb\run-4lhmsxsb.wandb
2025-03-10 15:34:20,416 DEBUG   SenderThread:19076 [sender.py:send():382] send: header
2025-03-10 15:34:20,445 DEBUG   SenderThread:19076 [sender.py:send():382] send: run
2025-03-10 15:34:20,454 INFO    SenderThread:19076 [sender.py:_maybe_setup_resume():763] checking resume status for None/FACT/4lhmsxsb
2025-03-10 15:34:21,693 ERROR   SenderThread:19076 [internal_api.py:execute():373] 403 response executing GraphQL.
2025-03-10 15:34:21,693 ERROR   SenderThread:19076 [internal_api.py:execute():374] {"errors":[{"message":"permission denied","path":["upsertBucket"],"extensions":{"code":"PERMISSION_ERROR"}}],"data":{"upsertBucket":null}}
2025-03-10 15:34:21,695 ERROR   SenderThread:19076 [sender.py:send_run():987] It appears that you do not have permission to access the requested resource. Please reach out to the project owner to grant you access. If you have the correct permissions, verify that there are no issues with your networking setup.(Error 403: Forbidden)
Traceback (most recent call last):
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\sdk\lib\retry.py", line 131, in __call__
    result = self._call_fn(*args, **kwargs)
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\sdk\internal\internal_api.py", line 369, in execute
    return self.client.execute(*args, **kwargs)  # type: ignore
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\vendor\gql-0.2.0\wandb_gql\client.py", line 52, in execute
    result = self._get_result(document, *args, **kwargs)
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\vendor\gql-0.2.0\wandb_gql\client.py", line 60, in _get_result
    return self.transport.execute(document, *args, **kwargs)
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\sdk\lib\gql_request.py", line 59, in execute
    request.raise_for_status()
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\requests\models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 403 Client Error: Forbidden for url: https://api.wandb.ai/graphql

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\sdk\internal\sender.py", line 985, in send_run
    self._init_run(run, config_value_dict)
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\sdk\internal\sender.py", line 1027, in _init_run
    server_run, inserted, server_messages = self._api.upsert_run(
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\apis\normalize.py", line 73, in wrapper
    raise err
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\apis\normalize.py", line 41, in wrapper
    return func(*args, **kwargs)
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\sdk\internal\internal_api.py", line 2216, in upsert_run
    response = self.gql(
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\sdk\internal\internal_api.py", line 341, in gql
    ret = self._retry_gql(
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\sdk\lib\retry.py", line 147, in __call__
    retry_timedelta_triggered = check_retry_fn(e)
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\util.py", line 965, in check_retry_fn
    return fallback_retry_fn(e)
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\util.py", line 907, in no_retry_auth
    raise CommError(
wandb.errors.CommError: It appears that you do not have permission to access the requested resource. Please reach out to the project owner to grant you access. If you have the correct permissions, verify that there are no issues with your networking setup.(Error 403: Forbidden)
2025-03-10 15:34:23,726 DEBUG   HandlerThread:19076 [handler.py:handle_request():146] handle_request: shutdown
2025-03-10 15:34:23,726 INFO    HandlerThread:19076 [handler.py:finish():869] shutting down handler
2025-03-10 15:34:24,472 INFO    WriterThread:19076 [datastore.py:close():296] close: log/breakfast\split1\0\wandb\run-20250310_153420-4lhmsxsb\run-4lhmsxsb.wandb
2025-03-10 15:34:24,723 INFO    SenderThread:19076 [sender.py:finish():1572] shutting down sender
