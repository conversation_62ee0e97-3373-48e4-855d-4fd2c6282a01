{"name": "org.mixedrealitytoolkit.data", "version": "1.0.3-development.pre.20", "description": "Data binding and theming features contained within this package are experimental, and are not recommended for use in production applications and may be changed or removed.", "displayName": "MRTK3 Data Binding and Theming (Experimental)", "msftFeatureCategory": "Experimental", "author": "Mixed Reality Toolkit Contributors", "license": "BSD 3-<PERSON><PERSON>", "repository": {"type": "git", "url": "https://github.com/MixedRealityToolkit/MixedRealityToolkit-Unity.git"}, "bugs": {"url": "https://github.com/MixedRealityToolkit/MixedRealityToolkit-Unity/issues"}, "unity": "2021.3", "unityRelease": "26f1", "documentationUrl": "https://www.mixedrealitytoolkit.org", "dependencies": {"org.mixedrealitytoolkit.core": "3.0.0", "com.unity.nuget.newtonsoft-json": "2.0.2", "com.unity.textmeshpro": "3.0.6"}}