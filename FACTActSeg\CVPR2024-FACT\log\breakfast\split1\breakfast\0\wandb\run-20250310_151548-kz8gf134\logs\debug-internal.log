2025-03-10 15:15:48,828 INFO    StreamThr :19944 [internal.py:wandb_internal():86] W&B internal server running at pid: 19944, started at: 2025-03-10 15:15:48.828054
2025-03-10 15:15:48,830 DEBUG   HandlerThread:19944 [handler.py:handle_request():146] handle_request: status
2025-03-10 15:15:48,831 INFO    WriterThread:19944 [datastore.py:open_for_write():87] open: log/breakfast\split1\breakfast\0\wandb\run-20250310_151548-kz8gf134\run-kz8gf134.wandb
2025-03-10 15:15:48,833 DEBUG   SenderThread:19944 [sender.py:send():382] send: header
2025-03-10 15:15:48,876 DEBUG   SenderThread:19944 [sender.py:send():382] send: run
2025-03-10 15:15:48,884 INFO    SenderThread:19944 [sender.py:_maybe_setup_resume():763] checking resume status for None/FACT/kz8gf134
2025-03-10 15:15:52,686 INFO    SenderThread:19944 [retry.py:__call__():172] Retry attempt failed:
urllib3.exceptions.SSLError: TLS/SSL connection has been closed (EOF) (_ssl.c:1149)

The above exception was the direct cause of the following exception:

urllib3.exceptions.ProxyError: ('Unable to connect to proxy', SSLError(SSLZeroReturnError(6, 'TLS/SSL connection has been closed (EOF) (_ssl.c:1149)')))

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\urllib3\connectionpool.py", line 843, in urlopen
    retries = retries.increment(
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\urllib3\util\retry.py", line 519, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='api.wandb.ai', port=443): Max retries exceeded with url: /graphql (Caused by ProxyError('Unable to connect to proxy', SSLError(SSLZeroReturnError(6, 'TLS/SSL connection has been closed (EOF) (_ssl.c:1149)'))))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\sdk\lib\retry.py", line 131, in __call__
    result = self._call_fn(*args, **kwargs)
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\sdk\internal\internal_api.py", line 369, in execute
    return self.client.execute(*args, **kwargs)  # type: ignore
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\vendor\gql-0.2.0\wandb_gql\client.py", line 52, in execute
    result = self._get_result(document, *args, **kwargs)
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\vendor\gql-0.2.0\wandb_gql\client.py", line 60, in _get_result
    return self.transport.execute(document, *args, **kwargs)
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\sdk\lib\gql_request.py", line 58, in execute
    request = self.session.post(self.url, **post_args)
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\requests\sessions.py", line 637, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\requests\adapters.py", line 694, in send
    raise ProxyError(e, request=request)
requests.exceptions.ProxyError: HTTPSConnectionPool(host='api.wandb.ai', port=443): Max retries exceeded with url: /graphql (Caused by ProxyError('Unable to connect to proxy', SSLError(SSLZeroReturnError(6, 'TLS/SSL connection has been closed (EOF) (_ssl.c:1149)'))))
2025-03-10 15:15:53,919 DEBUG   HandlerThread:19944 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:15:58,975 DEBUG   HandlerThread:19944 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:16:04,035 DEBUG   HandlerThread:19944 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:16:09,082 DEBUG   HandlerThread:19944 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:16:14,132 DEBUG   HandlerThread:19944 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:16:19,174 DEBUG   HandlerThread:19944 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:16:24,225 DEBUG   HandlerThread:19944 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:16:29,280 DEBUG   HandlerThread:19944 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:16:34,327 DEBUG   HandlerThread:19944 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:16:39,373 DEBUG   HandlerThread:19944 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:16:44,428 DEBUG   HandlerThread:19944 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:16:49,485 DEBUG   HandlerThread:19944 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:16:54,528 DEBUG   HandlerThread:19944 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:16:59,576 DEBUG   HandlerThread:19944 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:17:04,616 DEBUG   HandlerThread:19944 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:17:09,649 DEBUG   HandlerThread:19944 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:17:14,712 DEBUG   HandlerThread:19944 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:17:19,753 DEBUG   HandlerThread:19944 [handler.py:handle_request():146] handle_request: cancel
2025-03-10 15:17:19,753 DEBUG   HandlerThread:19944 [handler.py:handle_request():146] handle_request: cancel
2025-03-10 15:17:19,753 DEBUG   SenderThread:19944 [sender.py:send():391] Record cancelled: run
2025-03-10 15:17:19,753 DEBUG   HandlerThread:19944 [handler.py:handle_request():146] handle_request: status_report
2025-03-10 15:17:20,907 DEBUG   HandlerThread:19944 [handler.py:handle_request():146] handle_request: shutdown
2025-03-10 15:17:20,908 INFO    HandlerThread:19944 [handler.py:finish():869] shutting down handler
2025-03-10 15:17:21,758 INFO    SenderThread:19944 [sender.py:finish():1572] shutting down sender
2025-03-10 15:17:21,758 INFO    WriterThread:19944 [datastore.py:close():296] close: log/breakfast\split1\breakfast\0\wandb\run-20250310_151548-kz8gf134\run-kz8gf134.wandb
