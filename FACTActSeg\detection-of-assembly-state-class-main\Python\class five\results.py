class BaseFilterResult(object):
    def __init__(self, warning=False, wrong=False, missing=False):
        super().__init__()
        self.warning = warning
        self.wrong = wrong
        self.missing= missing


class YOLOFilterResult(BaseFilterResult):
    def __init__(self, img, class_names, warning=False, boxes=None, wrong=False, missing=False):
        super().__init__(warning, wrong, missing)
        if boxes is None:
            boxes = []
        self.boxes = boxes
        self.img = img
        self.class_names = class_names


class OCRFilterResult(BaseFilterResult):
    def __init__(self, img, warning=False, wrong=False, missing=False, texts=None):
        super().__init__(warning, wrong, missing)
        if texts is None:
            texts = []
        self.texts = texts
        self.img = img