import json
import os
import albumentations as A
import cv2
import numpy as np

# 将labelme文件转为yolo训练文件
def labelme2yolo(file_path):
    info = json.load(open(file_path,"r"))
    w = info["imageWidth"]
    h = info["imageHeight"]
    new = []
    for shape in info["shapes"]:
        x0, y0 = shape["points"][0]
        x1, y1 = shape["points"][1]
        x = (x1 + x0) / 2 / w
        y = (y1 + y0) / 2 / h
        width = abs(x1 - x0) / w
        height = abs(y1 - y0) / h
        new.append(" ".join([shape["label"], str(x), str(y), str(width), str(height)])+"\n")
    with open(file_path.replace("json", "txt"), "w") as f:
        f.writelines(new)




if __name__ == "__main__":
    json_dir = "./ud2/valid/labels"
    file_names = [each for each in os.listdir(json_dir) if "json" in each]
    for file_name in file_names:
        labelme2yolo(os.path.join(json_dir,file_name))
