2025-03-10 15:54:45,262 INFO    StreamThr :28808 [internal.py:wandb_internal():86] W&B internal server running at pid: 28808, started at: 2025-03-10 15:54:45.261859
2025-03-10 15:54:45,263 DEBUG   HandlerThread:28808 [handler.py:handle_request():146] handle_request: status
2025-03-10 15:54:45,268 INFO    WriterThread:28808 [datastore.py:open_for_write():87] open: log/breakfast\split1\0\wandb\run-20250310_155445-vxiqzb9c\run-vxiqzb9c.wandb
2025-03-10 15:54:45,268 DEBUG   SenderThread:28808 [sender.py:send():382] send: header
2025-03-10 15:54:45,298 DEBUG   SenderThread:28808 [sender.py:send():382] send: run
2025-03-10 15:54:45,307 INFO    SenderThread:28808 [sender.py:_maybe_setup_resume():763] checking resume status for zimingwang945/FACT/vxiqzb9c
2025-03-10 15:54:48,840 INFO    SenderThread:28808 [retry.py:__call__():172] Retry attempt failed:
Traceback (most recent call last):
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\urllib3\connectionpool.py", line 700, in urlopen
    self._prepare_proxy(conn)
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\urllib3\connectionpool.py", line 996, in _prepare_proxy
    conn.connect()
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\urllib3\connection.py", line 364, in connect
    self.sock = conn = self._connect_tls_proxy(hostname, conn)
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\urllib3\connection.py", line 499, in _connect_tls_proxy
    socket = ssl_wrap_socket(
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\urllib3\util\ssl_.py", line 453, in ssl_wrap_socket
    ssl_sock = _ssl_wrap_socket_impl(sock, context, tls_in_tls)
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\urllib3\util\ssl_.py", line 495, in _ssl_wrap_socket_impl
    return ssl_context.wrap_socket(sock)
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\ssl.py", line 500, in wrap_socket
    return self.sslsocket_class._create(
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\ssl.py", line 1073, in _create
    self.do_handshake()
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\ssl.py", line 1342, in do_handshake
    self._sslobj.do_handshake()
ssl.SSLZeroReturnError: TLS/SSL connection has been closed (EOF) (_ssl.c:1149)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    retries = retries.increment(
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\urllib3\util\retry.py", line 592, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='api.wandb.ai', port=443): Max retries exceeded with url: /graphql (Caused by SSLError(SSLZeroReturnError(6, 'TLS/SSL connection has been closed (EOF) (_ssl.c:1149)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\sdk\lib\retry.py", line 131, in __call__
    result = self._call_fn(*args, **kwargs)
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\sdk\internal\internal_api.py", line 369, in execute
    return self.client.execute(*args, **kwargs)  # type: ignore
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\vendor\gql-0.2.0\wandb_gql\client.py", line 52, in execute
    result = self._get_result(document, *args, **kwargs)
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\vendor\gql-0.2.0\wandb_gql\client.py", line 60, in _get_result
    return self.transport.execute(document, *args, **kwargs)
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\sdk\lib\gql_request.py", line 58, in execute
    request = self.session.post(self.url, **post_args)
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\requests\sessions.py", line 637, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\requests\adapters.py", line 698, in send
    raise SSLError(e, request=request)
requests.exceptions.SSLError: HTTPSConnectionPool(host='api.wandb.ai', port=443): Max retries exceeded with url: /graphql (Caused by SSLError(SSLZeroReturnError(6, 'TLS/SSL connection has been closed (EOF) (_ssl.c:1149)')))
2025-03-10 15:54:50,327 DEBUG   HandlerThread:28808 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:54:55,377 DEBUG   HandlerThread:28808 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:55:00,431 DEBUG   HandlerThread:28808 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:55:05,487 DEBUG   HandlerThread:28808 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:55:10,536 DEBUG   HandlerThread:28808 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:55:15,594 DEBUG   HandlerThread:28808 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:55:20,629 DEBUG   HandlerThread:28808 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:55:25,688 DEBUG   HandlerThread:28808 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:55:30,754 DEBUG   HandlerThread:28808 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:55:35,812 DEBUG   HandlerThread:28808 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:55:40,851 DEBUG   HandlerThread:28808 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:55:45,902 DEBUG   HandlerThread:28808 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:55:50,962 DEBUG   HandlerThread:28808 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:55:56,009 DEBUG   HandlerThread:28808 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:56:01,075 DEBUG   HandlerThread:28808 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:56:06,110 DEBUG   HandlerThread:28808 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:56:11,163 DEBUG   HandlerThread:28808 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:56:16,213 DEBUG   HandlerThread:28808 [handler.py:handle_request():146] handle_request: cancel
2025-03-10 15:56:16,213 DEBUG   HandlerThread:28808 [handler.py:handle_request():146] handle_request: cancel
2025-03-10 15:56:16,213 DEBUG   SenderThread:28808 [sender.py:send():391] Record cancelled: run
2025-03-10 15:56:16,213 DEBUG   HandlerThread:28808 [handler.py:handle_request():146] handle_request: status_report
2025-03-10 15:56:17,741 DEBUG   HandlerThread:28808 [handler.py:handle_request():146] handle_request: shutdown
2025-03-10 15:56:17,741 INFO    HandlerThread:28808 [handler.py:finish():869] shutting down handler
2025-03-10 15:56:18,226 INFO    WriterThread:28808 [datastore.py:close():296] close: log/breakfast\split1\0\wandb\run-20250310_155445-vxiqzb9c\run-vxiqzb9c.wandb
2025-03-10 15:56:18,226 INFO    SenderThread:28808 [sender.py:finish():1572] shutting down sender
