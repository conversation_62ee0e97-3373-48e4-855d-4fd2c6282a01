2025-03-10 15:18:37,732 INFO    MainThread:20808 [wandb_setup.py:_flush():76] Current SDK version is 0.16.2
2025-03-10 15:18:37,732 INFO    MainThread:20808 [wandb_setup.py:_flush():76] Configure stats pid to 20808
2025-03-10 15:18:37,732 INFO    MainThread:20808 [wandb_setup.py:_flush():76] Loading settings from C:\Users\<USER>\.config\wandb\settings
2025-03-10 15:18:37,732 INFO    MainThread:20808 [wandb_setup.py:_flush():76] Loading settings from C:\Users\<USER>\Desktop\FACTActSeg\CVPR2024-FACT\wandb\settings
2025-03-10 15:18:37,732 INFO    MainThread:20808 [wandb_setup.py:_flush():76] Loading settings from environment variables: {}
2025-03-10 15:18:37,732 INFO    MainThread:20808 [wandb_setup.py:_flush():76] Applying setup settings: {'_disable_service': False}
2025-03-10 15:18:37,732 INFO    MainThread:20808 [wandb_setup.py:_flush():76] Inferring run settings from compute environment: {'program_relpath': 'train.py', 'program_abspath': 'C:\\Users\\<USER>\\Desktop\\FACTActSeg\\CVPR2024-FACT\\train.py', 'program': 'train.py'}
2025-03-10 15:18:37,732 INFO    MainThread:20808 [wandb_init.py:_log_setup():526] Logging user logs to log/breakfast\split1\breakfast\0\wandb\run-20250310_151837-7dod3khb\logs\debug.log
2025-03-10 15:18:37,732 INFO    MainThread:20808 [wandb_init.py:_log_setup():527] Logging internal logs to log/breakfast\split1\breakfast\0\wandb\run-20250310_151837-7dod3khb\logs\debug-internal.log
2025-03-10 15:18:37,732 INFO    MainThread:20808 [wandb_init.py:init():566] calling init triggers
2025-03-10 15:18:37,732 INFO    MainThread:20808 [wandb_init.py:init():573] wandb.init called with sweep_config: {}
config: {'aux.gpu': 0, 'aux.mark': '', 'aux.runid': 0, 'aux.debug': False, 'aux.wandb_project': 'FACT', 'aux.wandb_user': '', 'aux.wandb_offline': False, 'aux.resume': 'max', 'aux.eval_every': 2000, 'aux.print_every': 1000, 'aux.cfg_file': "['configs/breakfast.yaml']", 'aux.set_cfgs': 'None', 'aux.exp': 'breakfast', 'aux.logdir': 'log/breakfast\\split1\\breakfast\\0', 'dataset': 'breakfast', 'split': 'split1', 'sr': 1, 'eval_bg': True, 'batch_size': 4, 'optimizer': 'Adam', 'epoch': 150, 'lr': 0.0001, 'lr_decay': 80, 'momentum': 0.0, 'weight_decay': 0.0, 'clip_grad_norm': 10.0, 'FACT.ntoken': 60, 'FACT.block': 'iuUU', 'FACT.trans': False, 'FACT.fpos': False, 'FACT.cmr': 0.3, 'FACT.mwt': 0.1, 'Bi.hid_dim': 512, 'Bi.dropout': 0.0, 'Bi.a': 'sca', 'Bi.a_nhead': 8, 'Bi.a_ffdim': 512, 'Bi.a_layers': 6, 'Bi.a_dim': 512, 'Bi.f': 'm2', 'Bi.f_layers': 10, 'Bi.f_ln': False, 'Bi.f_dim': 512, 'Bi.f_ngp': 1, 'Bu.hid_dim': 'None', 'Bu.dropout': 'None', 'Bu.a': 'sa', 'Bu.a_nhead': 8, 'Bu.a_ffdim': 'None', 'Bu.a_layers': 1, 'Bu.a_dim': 'None', 'Bu.f': 'None', 'Bu.f_layers': 10, 'Bu.f_ln': 'None', 'Bu.f_dim': 'None', 'Bu.f_ngp': 'None', 'BU.hid_dim': 'None', 'BU.dropout': 'None', 'BU.a': 'sa', 'BU.a_nhead': 8, 'BU.a_ffdim': 'None', 'BU.a_layers': 1, 'BU.a_dim': 'None', 'BU.f': 'None', 'BU.f_layers': 10, 'BU.f_ln': 'None', 'BU.f_dim': 'None', 'BU.f_ngp': 'None', 'BU.s_layers': 1, 'Loss.pc': 0.2, 'Loss.a2fc': 1.0, 'Loss.match': 'o2o', 'Loss.bgw': 1.0, 'Loss.nullw': -1.0, 'Loss.sw': 5.0, 'TM.use': True, 'TM.t': 30, 'TM.p': 0.05, 'TM.m': 5, 'TM.inplace': True}
2025-03-10 15:18:37,732 INFO    MainThread:20808 [wandb_init.py:init():616] starting backend
2025-03-10 15:18:37,734 INFO    MainThread:20808 [wandb_init.py:init():620] setting up manager
2025-03-10 15:18:37,735 INFO    MainThread:20808 [backend.py:_multiprocessing_setup():105] multiprocessing start_methods=spawn, using: spawn
2025-03-10 15:18:37,736 INFO    MainThread:20808 [wandb_init.py:init():628] backend started and connected
2025-03-10 15:18:37,738 INFO    MainThread:20808 [wandb_init.py:init():720] updated telemetry
2025-03-10 15:18:37,765 INFO    MainThread:20808 [wandb_init.py:init():753] communicating run to backend with 90.0 second timeout
2025-03-10 15:18:38,768 ERROR   MainThread:20808 [wandb_init.py:init():779] encountered error: It appears that you do not have permission to access the requested resource. Please reach out to the project owner to grant you access. If you have the correct permissions, verify that there are no issues with your networking setup.(Error 403: Forbidden)
2025-03-10 15:18:39,454 ERROR   MainThread:20808 [wandb_init.py:init():1194] It appears that you do not have permission to access the requested resource. Please reach out to the project owner to grant you access. If you have the correct permissions, verify that there are no issues with your networking setup.(Error 403: Forbidden)
Traceback (most recent call last):
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\sdk\wandb_init.py", line 1176, in init
    run = wi.init()
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\sdk\wandb_init.py", line 785, in init
    raise error
wandb.errors.CommError: It appears that you do not have permission to access the requested resource. Please reach out to the project owner to grant you access. If you have the correct permissions, verify that there are no issues with your networking setup.(Error 403: Forbidden)
2025-03-10 15:18:40,460 WARNING MsgRouterThr:20808 [router.py:message_loop():77] message_loop has been closed
