import threading
import time

# 线程函数
def fun1(flag:str, x:int)->None:
    for i in range(x):
        print(flag + ":" + str(i))
        time.sleep(1)
        
if __name__ == "__main__":
    # 打印5次
    th1 = threading.Thread(target=fun1, args=("th1",5,))
    # 打印10次
    th2 = threading.Thread(target=fun1, args=("th2",10,))
    # 线程守护
    th1.daemon = True
    th2.daemon = True
    # 开启线程
    th1.start()
    th2.start()
    # 等待线程结束
    th1.join()
    th2.join()
    