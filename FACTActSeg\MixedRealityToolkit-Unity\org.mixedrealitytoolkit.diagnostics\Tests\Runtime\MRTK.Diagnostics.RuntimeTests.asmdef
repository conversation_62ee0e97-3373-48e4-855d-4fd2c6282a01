{"name": "MixedReality.Toolkit.Diagnostics.Runtime.Tests", "rootNamespace": "", "references": ["MixedReality.Toolkit.Diagnostics", "UnityEditor.TestRunner", "UnityEngine.TestRunner"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": true, "precompiledReferences": ["nunit.framework.dll"], "autoReferenced": false, "defineConstraints": ["UNITY_INCLUDE_TESTS"], "versionDefines": [], "noEngineReferences": false}