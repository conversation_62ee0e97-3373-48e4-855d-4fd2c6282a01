import json
import random

class Game:
    def __init__(self, conf):
        with open(conf) as f:
            config = json.load(f)
            self.min = config["min"]
            self.max = config["max"]
            self.times = config["times"]

    def main(self):
        num = random.randint(self.min, self.max)
        wrong_times = 0
        while True:
            if wrong_times >= self.times:
                print("机会用完了，下次再试试吧")
                break
            guess = int(input("猜猜我是谁>>>"))
            if guess > num:
                wrong_times += 1
                print("猜大啦")
            elif guess < num:
                wrong_times += 1
                print("猜小啦")
            else:
                print("恭喜你猜对了")
                break

if __name__ == "__main__":
    game = Game("./config.json")
    game.main()
