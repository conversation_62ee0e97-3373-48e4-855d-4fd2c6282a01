2025-03-10 15:20:17,939 INFO    StreamThr :27128 [internal.py:wandb_internal():86] W&B internal server running at pid: 27128, started at: 2025-03-10 15:20:17.939890
2025-03-10 15:20:17,941 DEBUG   HandlerThread:27128 [handler.py:handle_request():146] handle_request: status
2025-03-10 15:20:17,943 INFO    WriterThread:27128 [datastore.py:open_for_write():87] open: log/breakfast\split1\breakfast\0\wandb\run-20250310_152017-cezksujc\run-cezksujc.wandb
2025-03-10 15:20:17,944 DEBUG   SenderThread:27128 [sender.py:send():382] send: header
2025-03-10 15:20:18,012 DEBUG   SenderThread:27128 [sender.py:send():382] send: run
2025-03-10 15:20:18,020 INFO    SenderThread:27128 [sender.py:_maybe_setup_resume():763] checking resume status for None/FACT/cezksujc
2025-03-10 15:20:22,235 INFO    SenderThread:27128 [retry.py:__call__():172] Retry attempt failed:
urllib3.exceptions.SSLError: TLS/SSL connection has been closed (EOF) (_ssl.c:1149)

The above exception was the direct cause of the following exception:

urllib3.exceptions.ProxyError: ('Unable to connect to proxy', SSLError(SSLZeroReturnError(6, 'TLS/SSL connection has been closed (EOF) (_ssl.c:1149)')))

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\urllib3\connectionpool.py", line 843, in urlopen
    retries = retries.increment(
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\urllib3\util\retry.py", line 519, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='api.wandb.ai', port=443): Max retries exceeded with url: /graphql (Caused by ProxyError('Unable to connect to proxy', SSLError(SSLZeroReturnError(6, 'TLS/SSL connection has been closed (EOF) (_ssl.c:1149)'))))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\sdk\lib\retry.py", line 131, in __call__
    result = self._call_fn(*args, **kwargs)
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\sdk\internal\internal_api.py", line 369, in execute
    return self.client.execute(*args, **kwargs)  # type: ignore
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\vendor\gql-0.2.0\wandb_gql\client.py", line 52, in execute
    result = self._get_result(document, *args, **kwargs)
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\vendor\gql-0.2.0\wandb_gql\client.py", line 60, in _get_result
    return self.transport.execute(document, *args, **kwargs)
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\sdk\lib\gql_request.py", line 58, in execute
    request = self.session.post(self.url, **post_args)
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\requests\sessions.py", line 637, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\requests\adapters.py", line 694, in send
    raise ProxyError(e, request=request)
requests.exceptions.ProxyError: HTTPSConnectionPool(host='api.wandb.ai', port=443): Max retries exceeded with url: /graphql (Caused by ProxyError('Unable to connect to proxy', SSLError(SSLZeroReturnError(6, 'TLS/SSL connection has been closed (EOF) (_ssl.c:1149)'))))
2025-03-10 15:20:23,044 DEBUG   HandlerThread:27128 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:20:28,097 DEBUG   HandlerThread:27128 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:20:33,151 DEBUG   HandlerThread:27128 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:20:38,193 DEBUG   HandlerThread:27128 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:20:43,255 DEBUG   HandlerThread:27128 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:20:48,310 DEBUG   HandlerThread:27128 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:20:53,343 DEBUG   HandlerThread:27128 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:20:58,390 DEBUG   HandlerThread:27128 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:21:03,428 DEBUG   HandlerThread:27128 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:21:08,491 DEBUG   HandlerThread:27128 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:21:13,528 DEBUG   HandlerThread:27128 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:21:18,586 DEBUG   HandlerThread:27128 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:21:23,644 DEBUG   HandlerThread:27128 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:21:28,691 DEBUG   HandlerThread:27128 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:21:33,737 DEBUG   HandlerThread:27128 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:21:38,771 DEBUG   HandlerThread:27128 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:21:43,824 DEBUG   HandlerThread:27128 [handler.py:handle_request():146] handle_request: keepalive
2025-03-10 15:21:48,868 DEBUG   HandlerThread:27128 [handler.py:handle_request():146] handle_request: cancel
2025-03-10 15:21:48,868 DEBUG   HandlerThread:27128 [handler.py:handle_request():146] handle_request: cancel
2025-03-10 15:21:48,868 DEBUG   SenderThread:27128 [sender.py:send():391] Record cancelled: run
2025-03-10 15:21:48,868 DEBUG   HandlerThread:27128 [handler.py:handle_request():146] handle_request: status_report
2025-03-10 15:21:50,068 DEBUG   HandlerThread:27128 [handler.py:handle_request():146] handle_request: shutdown
2025-03-10 15:21:50,068 INFO    HandlerThread:27128 [handler.py:finish():869] shutting down handler
2025-03-10 15:21:50,889 INFO    SenderThread:27128 [sender.py:finish():1572] shutting down sender
2025-03-10 15:21:50,889 INFO    WriterThread:27128 [datastore.py:close():296] close: log/breakfast\split1\breakfast\0\wandb\run-20250310_152017-cezksujc\run-cezksujc.wandb
