using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Player : MonoBeh<PERSON>our
{
    public float moveSpeed = 0.3f;
    public float rotateSpeed = 5f;
    float rotateX = 0;
    float rotateY = 0;
    // Start is called before the first frame update
    void Start()
    {
        rotateX = transform.rotation.eulerAngles.x;
        rotateY = transform.rotation.eulerAngles.y;
    }

    // Update is called once per frame
    void Update()
    {
        
    }
    private void FixedUpdate()
    {
        if (Input.GetKey(KeyCode.W))
        {
            transform.Translate(moveSpeed, 0, 0);
        }
        if (Input.GetKey(KeyCode.S))
        {
            transform.Translate(-moveSpeed, 0, 0);
        }
        if (Input.GetKey(KeyCode.A))
        {
            transform.Translate(0, 0, moveSpeed);
        }
        if (Input.GetKey(KeyCode.D))
        {
            transform.Translate(0, 0, -moveSpeed);
        }
        float Rx = Input.GetAxis("Mouse X");
        float Ry = Input.GetAxis("Mouse Y");
        //Debug.Log(Quaternion.Euler(rotateX + Rx * rotateSpeed, rotateY + Ry * rotateSpeed, 0));
        //Debug.Log(rotateX + Rx * rotateSpeed);
        //Debug.Log(rotateY + Ry * rotateSpeed);
        rotateX -= Rx * rotateSpeed;
        rotateY -= Ry * rotateSpeed;
        transform.rotation = Quaternion.Euler(rotateY, rotateX, 0);

    }
}
