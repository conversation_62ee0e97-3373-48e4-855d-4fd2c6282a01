2025-03-10 15:46:06,227 INFO    MainThread:32232 [wandb_setup.py:_flush():76] Current SDK version is 0.16.2
2025-03-10 15:46:06,227 INFO    MainThread:32232 [wandb_setup.py:_flush():76] Configure stats pid to 32232
2025-03-10 15:46:06,227 INFO    MainThread:32232 [wandb_setup.py:_flush():76] Loading settings from C:\Users\<USER>\.config\wandb\settings
2025-03-10 15:46:06,227 INFO    MainThread:32232 [wandb_setup.py:_flush():76] Loading settings from C:\Users\<USER>\Desktop\FACTActSeg\CVPR2024-FACT\wandb\settings
2025-03-10 15:46:06,227 INFO    MainThread:32232 [wandb_setup.py:_flush():76] Loading settings from environment variables: {'api_key': '***REDACTED***'}
2025-03-10 15:46:06,227 INFO    MainThread:32232 [wandb_setup.py:_flush():76] Applying setup settings: {'_disable_service': False}
2025-03-10 15:46:06,229 INFO    MainThread:32232 [wandb_setup.py:_flush():76] Inferring run settings from compute environment: {'program_relpath': 'train.py', 'program_abspath': 'c:\\Users\\<USER>\\Desktop\\FACTActSeg\\CVPR2024-FACT\\train.py', 'program': 'c:\\Users\\<USER>\\Desktop\\FACTActSeg\\CVPR2024-FACT\\train.py'}
2025-03-10 15:46:06,229 INFO    MainThread:32232 [wandb_init.py:_log_setup():526] Logging user logs to log/breakfast\split1\0\wandb\run-20250310_154606-4hlsaj63\logs\debug.log
2025-03-10 15:46:06,229 INFO    MainThread:32232 [wandb_init.py:_log_setup():527] Logging internal logs to log/breakfast\split1\0\wandb\run-20250310_154606-4hlsaj63\logs\debug-internal.log
2025-03-10 15:46:06,229 INFO    MainThread:32232 [wandb_init.py:init():566] calling init triggers
2025-03-10 15:46:06,229 INFO    MainThread:32232 [wandb_init.py:init():573] wandb.init called with sweep_config: {}
config: {'aux.gpu': 1, 'aux.mark': '', 'aux.runid': 0, 'aux.debug': False, 'aux.wandb_project': 'FACT', 'aux.wandb_user': '', 'aux.wandb_offline': False, 'aux.resume': 'max', 'aux.eval_every': 1000, 'aux.print_every': 200, 'aux.cfg_file': '[]', 'aux.set_cfgs': 'None', 'aux.exp': '', 'aux.logdir': 'log/breakfast\\split1\\0', 'dataset': 'breakfast', 'split': 'split1', 'sr': 1, 'eval_bg': False, 'batch_size': 4, 'optimizer': 'SGD', 'epoch': 2, 'lr': 0.1, 'lr_decay': -1, 'momentum': 0.009, 'weight_decay': 0.0, 'clip_grad_norm': 10.0, 'FACT.ntoken': 30, 'FACT.block': 'iuUU', 'FACT.trans': False, 'FACT.fpos': True, 'FACT.cmr': 0.3, 'FACT.mwt': 0.1, 'Bi.hid_dim': 512, 'Bi.dropout': 0.5, 'Bi.a': 'sca', 'Bi.a_nhead': 8, 'Bi.a_ffdim': 2048, 'Bi.a_layers': 6, 'Bi.a_dim': 512, 'Bi.f': 'cnn', 'Bi.f_layers': 10, 'Bi.f_ln': True, 'Bi.f_dim': 512, 'Bi.f_ngp': 4, 'Bu.hid_dim': 'None', 'Bu.dropout': 'None', 'Bu.a': 'sa', 'Bu.a_nhead': 'None', 'Bu.a_ffdim': 'None', 'Bu.a_layers': 1, 'Bu.a_dim': 'None', 'Bu.f': 'None', 'Bu.f_layers': 5, 'Bu.f_ln': 'None', 'Bu.f_dim': 'None', 'Bu.f_ngp': 'None', 'BU.hid_dim': 'None', 'BU.dropout': 'None', 'BU.a': 'sa', 'BU.a_nhead': 'None', 'BU.a_ffdim': 'None', 'BU.a_layers': 1, 'BU.a_dim': 'None', 'BU.f': 'None', 'BU.f_layers': 5, 'BU.f_ln': 'None', 'BU.f_dim': 'None', 'BU.f_ngp': 'None', 'BU.s_layers': 1, 'Loss.pc': 1.0, 'Loss.a2fc': 1.0, 'Loss.match': 'o2o', 'Loss.bgw': 1.0, 'Loss.nullw': -1.0, 'Loss.sw': 0.0, 'TM.use': False, 'TM.t': 30, 'TM.p': 0.05, 'TM.m': 5, 'TM.inplace': True}
2025-03-10 15:46:06,229 INFO    MainThread:32232 [wandb_init.py:init():616] starting backend
2025-03-10 15:46:06,229 INFO    MainThread:32232 [wandb_init.py:init():620] setting up manager
2025-03-10 15:46:06,232 INFO    MainThread:32232 [backend.py:_multiprocessing_setup():105] multiprocessing start_methods=spawn, using: spawn
2025-03-10 15:46:06,233 INFO    MainThread:32232 [wandb_init.py:init():628] backend started and connected
2025-03-10 15:46:06,238 INFO    MainThread:32232 [wandb_init.py:init():720] updated telemetry
2025-03-10 15:46:06,267 INFO    MainThread:32232 [wandb_init.py:init():753] communicating run to backend with 90.0 second timeout
2025-03-10 15:46:07,230 ERROR   MainThread:32232 [wandb_init.py:init():779] encountered error: It appears that you do not have permission to access the requested resource. Please reach out to the project owner to grant you access. If you have the correct permissions, verify that there are no issues with your networking setup.(Error 404: Not Found)
2025-03-10 15:46:07,927 ERROR   MainThread:32232 [wandb_init.py:init():1194] It appears that you do not have permission to access the requested resource. Please reach out to the project owner to grant you access. If you have the correct permissions, verify that there are no issues with your networking setup.(Error 404: Not Found)
Traceback (most recent call last):
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\sdk\wandb_init.py", line 1176, in init
    run = wi.init()
  File "E:\Softwares\MiniConda3py312_25.1.1-2\envs\fact\lib\site-packages\wandb\sdk\wandb_init.py", line 785, in init
    raise error
wandb.errors.CommError: It appears that you do not have permission to access the requested resource. Please reach out to the project owner to grant you access. If you have the correct permissions, verify that there are no issues with your networking setup.(Error 404: Not Found)
2025-03-10 15:46:08,942 WARNING MsgRouterThr:32232 [router.py:message_loop():77] message_loop has been closed
