import socket
import struct

if __name__ == '__main__':
    client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    packer = struct.Struct("I")
    client.connect(("127.0.0.1", 9090))
    while True:
        recv_data_len = packer.unpack(client.recv(4))[0]
        recv_data = client.recv(recv_data_len).decode("utf-8")
        print("recv_data: ", recv_data)

        send_data = input("input something >>>").encode("utf-8")
        send_data_len = len(send_data)
        client.send(packer.pack(send_data_len))
        client.send(send_data)
        
        

