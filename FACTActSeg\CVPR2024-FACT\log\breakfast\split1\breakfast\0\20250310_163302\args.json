{"aux": {"gpu": 0, "mark": "", "runid": 0, "debug": false, "wandb_project": "FACT", "wandb_user": "", "wandb_offline": false, "resume": "max", "eval_every": 2000, "print_every": 1000, "cfg_file": ["configs/breakfast.yaml"], "set_cfgs": null, "exp": "breakfast", "logdir": "log/breakfast\\split1\\breakfast\\0"}, "dataset": "breakfast", "split": "split1", "sr": 1, "eval_bg": true, "batch_size": 4, "optimizer": "<PERSON>", "epoch": 150, "lr": 0.0001, "lr_decay": 80, "momentum": 0.0, "weight_decay": 0.0, "clip_grad_norm": 10.0, "FACT": {"ntoken": 60, "block": "iuUU", "trans": false, "fpos": false, "cmr": 0.3, "mwt": 0.1}, "Bi": {"hid_dim": 512, "dropout": 0.0, "a": "sca", "a_nhead": 8, "a_ffdim": 512, "a_layers": 6, "a_dim": 512, "f": "m2", "f_layers": 10, "f_ln": false, "f_dim": 512, "f_ngp": 1}, "Bu": {"hid_dim": null, "dropout": null, "a": "sa", "a_nhead": 8, "a_ffdim": null, "a_layers": 1, "a_dim": null, "f": null, "f_layers": 10, "f_ln": null, "f_dim": null, "f_ngp": null}, "BU": {"hid_dim": null, "dropout": null, "a": "sa", "a_nhead": 8, "a_ffdim": null, "a_layers": 1, "a_dim": null, "f": null, "f_layers": 10, "f_ln": null, "f_dim": null, "f_ngp": null, "s_layers": 1}, "Loss": {"pc": 0.2, "a2fc": 1.0, "match": "o2o", "bgw": 1.0, "nullw": -1.0, "sw": 5.0}, "TM": {"use": true, "t": 30, "p": 0.05, "m": 5, "inplace": true}}