{"name": "MixedReality.Toolkit.Data.Editor.Tests", "rootNamespace": "", "references": ["MixedReality.Toolkit.Core.TestUtilities", "UnityEngine.TestRunner", "UnityEditor.TestRunner"], "includePlatforms": ["Editor"], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": true, "precompiledReferences": ["nunit.framework.dll"], "autoReferenced": false, "defineConstraints": ["UNITY_INCLUDE_TESTS"], "versionDefines": [{"name": "com.unity.asset-store-validation", "expression": "", "define": "HAS_ASSET_STORE_VALIDATION"}], "noEngineReferences": false}