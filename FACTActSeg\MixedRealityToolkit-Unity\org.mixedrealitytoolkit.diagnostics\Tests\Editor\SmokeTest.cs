// Copyright (c) Mixed Reality Toolkit Contributors
// Licensed under the BSD 3-Clause

// Disable "missing XML comment" warning for tests. While nice to have, this documentation is not required.
#pragma warning disable CS1591

using NUnit.Framework;

namespace MixedReality.Toolkit.Performance.Tests.EditMode
{
    internal class SmokeTest
    {
        [Test]
        public void PerformancePackageTest()
        {
            Assert.IsTrue(true);
        }
    }
}
#pragma warning restore CS1591