%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 9
  m_Shader: {fileID: 4800000, guid: c331f6c43a2ef0945864cb668f2653c9, type: 3}
  m_ValidKeywords:
  - _DIRECTIONAL_LIGHT
  - _REFLECTIONS
  - _SPECULAR_HIGHLIGHTS
  - _USE_WORLD_SCALE
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 2000
  stringTagMap:
    RenderType: Opaque
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ChannelMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _IridescentSpectrumMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: c78ef35bd9ed8894fb30e1d6ac8c78af, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlbedoAlphaMode: 0
    - _AlbedoAssignedAtRuntime: 0
    - _BlendOp: 0
    - _BlendedClippingWidth: 1
    - _BlurBorderIntensity: 0
    - _BlurMode: 0
    - _BlurTextureIntensity: 1
    - _BorderColorMode: 0
    - _BorderLight: 0
    - _BorderLightOpaque: 0
    - _BorderLightOpaqueAlpha: 1
    - _BorderLightReplacesAlbedo: 0
    - _BorderLightUsesHoverColor: 0
    - _BorderMinValue: 0.1
    - _BorderWidth: 0.1
    - _BumpScale: 1
    - _ClippingBorder: 0
    - _ClippingBorderWidth: 0.025
    - _ColorWriteMask: 15
    - _CullMode: 2
    - _CustomMode: 0
    - _Cutoff: 0.5
    - _DetailNormalMapScale: 1
    - _DirectionalLight: 1
    - _DstBlend: 0
    - _EdgeSmoothingMode: 0
    - _EdgeSmoothingValue: 0.002
    - _EnableChannelMap: 0
    - _EnableEmission: 0
    - _EnableHoverColorOverride: 0
    - _EnableLocalSpaceTriplanarMapping: 0
    - _EnableNormalMap: 0
    - _EnableProximityLightColorOverride: 0
    - _EnableSSAA: 0
    - _EnableStencil: 0
    - _EnableTriplanarMapping: 0
    - _EnvironmentColorIntensity: 0.5
    - _EnvironmentColorThreshold: 1.5
    - _EnvironmentColoring: 0
    - _Fade: 1
    - _FadeBeginDistance: 0.85
    - _FadeCompleteDistance: 0.5
    - _FadeMinValue: 0
    - _FluentLightIntensity: 1
    - _GlossMapScale: 1
    - _Glossiness: 0
    - _GlossyReflections: 1
    - _GradientAngle: 180
    - _GradientMode: 0
    - _HoverLight: 0
    - _IndependentCorners: 0
    - _InnerGlow: 0
    - _InnerGlowPower: 4
    - _InstancedColor: 0
    - _Iridescence: 0
    - _IridescenceAngle: -0.78
    - _IridescenceIntensity: 0.5
    - _IridescenceThreshold: 0.05
    - _Metallic: 0
    - _MipmapBias: -2
    - _Mode: 0
    - _NearLightFade: 0
    - _NearPlaneFade: 0
    - _NormalMapScale: 1
    - _OcclusionStrength: 1
    - _Parallax: 0.02
    - _ProximityLight: 0
    - _ProximityLightSubtractive: 0
    - _ProximityLightTwoSided: 0
    - _Reflections: 1
    - _Refraction: 0
    - _RefractiveIndex: 0
    - _RenderQueueOverride: -1
    - _RimLight: 0
    - _RimPower: 0.25
    - _RoundCornerMargin: 0.01
    - _RoundCornerRadius: 0.25
    - _RoundCorners: 0
    - _RoundCornersHideInterior: 0
    - _Smoothness: 0
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SphericalHarmonics: 0
    - _SrcBlend: 1
    - _Stencil: 0
    - _StencilComparison: 0
    - _StencilOperation: 0
    - _StencilReadMask: 255
    - _StencilReference: 0
    - _StencilWriteMask: 255
    - _TriplanarMappingBlendSharpness: 4
    - _UVSec: 0
    - _UseWorldScale: 1
    - _VertexColors: 0
    - _VertexExtrusion: 0
    - _VertexExtrusionSmoothNormals: 0
    - _VertexExtrusionValue: 0
    - _ZOffsetFactor: 0
    - _ZOffsetUnits: 0
    - _ZTest: 4
    - _ZWrite: 1
    m_Colors:
    - _BlurBackgroundRect: {r: 0, g: 0, b: 1, a: 1}
    - _BorderColor: {r: 1, g: 1, b: 1, a: 0}
    - _ClippingBorderColor: {r: 1, g: 0.2, b: 0, a: 1}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 0}
    - _EmissiveColor: {r: 0, g: 0, b: 0, a: 0}
    - _EnvironmentColorX: {r: 1, g: 0, b: 0, a: 1}
    - _EnvironmentColorY: {r: 0, g: 1, b: 0, a: 1}
    - _EnvironmentColorZ: {r: 0, g: 0, b: 1, a: 1}
    - _GradientColor0: {r: 0.631373, g: 0.631373, b: 0.631373, a: 0}
    - _GradientColor1: {r: 1, g: 0.690196, b: 0.976471, a: 0.25}
    - _GradientColor2: {r: 0, g: 0.33, b: 0.88, a: 0.5}
    - _GradientColor3: {r: 0, g: 0.33, b: 0.88, a: 1}
    - _GradientColor4: {r: 1, g: 1, b: 1, a: 1}
    - _HoverColorOverride: {r: 1, g: 1, b: 1, a: 1}
    - _InnerGlowColor: {r: 1, g: 1, b: 1, a: 0.75}
    - _ProximityLightCenterColorOverride: {r: 1, g: 0, b: 0, a: 0}
    - _ProximityLightMiddleColorOverride: {r: 0, g: 1, b: 0, a: 0.5}
    - _ProximityLightOuterColorOverride: {r: 0, g: 0, b: 1, a: 1}
    - _RimColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
    - _RoundCornersRadius: {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
  m_BuildTextureStacks: []
