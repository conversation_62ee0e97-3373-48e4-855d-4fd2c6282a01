; FBX 7.5.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7500
	CreationTimeStamp:  {
		Version: 1000
		Year: 2016
		Month: 11
		Day: 3
		Hour: 14
		Minute: 36
		Second: 46
		Millisecond: 6
	}
	Creator: "FBX SDK/FBX Plugins version 2016.0"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "C:\Users\<USER>\Desktop\Primatives\scenes\cheveron.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "C:\Users\<USER>\Desktop\Primatives\scenes\cheveron.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", "Autodesk"
			P: "Original|ApplicationName", "KString", "", "", "Maya"
			P: "Original|ApplicationVersion", "KString", "", "", "2016"
			P: "Original|DateTime_GMT", "DateTime", "", "", "03/11/2016 21:36:46.004"
			P: "Original|FileName", "KString", "", "", "C:/Users/<USER>/Desktop/Primatives/scenes/cheveron.fbx"
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", "Autodesk"
			P: "LastSaved|ApplicationName", "KString", "", "", "Maya"
			P: "LastSaved|ApplicationVersion", "KString", "", "", "2016"
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", "03/11/2016 21:36:46.004"
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",1
		P: "OriginalUnitScaleFactor", "double", "Number", "",1
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeProtocol", "enum", "", "",2
		P: "SnapOnFrameMode", "enum", "", "",0
		P: "TimeSpanStart", "KTime", "Time", "",1924423250
		P: "TimeSpanStop", "KTime", "Time", "",384884650000
		P: "CustomFrameRate", "double", "Number", "",-1
		P: "TimeMarker", "Compound", "", ""
		P: "CurrentTimeMarker", "int", "Integer", "",-1
	}
}

; Documents Description
;------------------------------------------------------------------

Documents:  {
	Count: 1
	Document: 2407284730208, "", "Scene" {
		Properties70:  {
			P: "SourceObject", "object", "", ""
			P: "ActiveAnimStackName", "KString", "", "", "Take 001"
		}
		RootNode: 0
	}
}

; Document References
;------------------------------------------------------------------

References:  {
}

; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 6
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "AnimationStack" {
		Count: 1
		PropertyTemplate: "FbxAnimStack" {
			Properties70:  {
				P: "Description", "KString", "", "", ""
				P: "LocalStart", "KTime", "Time", "",0
				P: "LocalStop", "KTime", "Time", "",0
				P: "ReferenceStart", "KTime", "Time", "",0
				P: "ReferenceStop", "KTime", "Time", "",0
			}
		}
	}
	ObjectType: "AnimationLayer" {
		Count: 1
		PropertyTemplate: "FbxAnimLayer" {
			Properties70:  {
				P: "Weight", "Number", "", "A",100
				P: "Mute", "bool", "", "",0
				P: "Solo", "bool", "", "",0
				P: "Lock", "bool", "", "",0
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BlendMode", "enum", "", "",0
				P: "RotationAccumulationMode", "enum", "", "",0
				P: "ScaleAccumulationMode", "enum", "", "",0
				P: "BlendModeBypass", "ULongLong", "", "",0
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfaceLambert" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Lambert"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
			}
		}
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Geometry: 2405685750912, "Geometry::", "Mesh" {
		Vertices: *36 {
			a: 0,-13.1516590118408,6.91053009033203,49.1948699951172,-49.7178688049316,6.91053009033203,-49.1201019287109,-49.4226570129395,6.91053009033203,0,49.8396644592285,6.91053009033203,-49.1201019287109,-49.4226570129395,-6.91053009033203,0,49.8396644592285,-6.91053009033203,0,-13.1516590118408,-6.91053009033203,49.1948699951172,-49.7178688049316,-6.91053009033203,48.5966110229492,-0.580095291137695,-6.91053009033203,-48.8925399780273,-0.507038116455078,-6.91053009033203,-48.8763809204102,-0.474376678466797,6.91053009033203,48.7227249145508,-0.580892562866211,6.91053009033203
		} 
		PolygonVertexIndex: *60 {
			a: 10,11,-4,9,10,-6,5,10,-4,8,9,-6,6,7,-1,0,7,-2,11,8,-4,3,8,-6,6,0,-5,4,0,-3,4,9,-7,9,8,-7,8,7,-7,2,10,-5,4,10,-10,1,11,-1,11,10,-1,0,10,-3,1,7,-12,11,7,-9
		} 
		Edges: *30 {
			a: 17,39,30,12,28,45,29,8,26,11,14,16,36,5,2,1,9,3,0,18,4,13,19,25,31,34,40,46,49,55
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 102
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *180 {
				a: 5.44038503136335e-009,0,0.999999940395355,-5.44747313924177e-009,0,1,0,0,1,-0.896532595157623,0.44297781586647,1.29608179122442e-006,-0.895998001098633,0.444058060646057,-2.8099566407036e-005,-0.717293977737427,0.696770668029785,-0.00010007726814365,-0.717293977737427,0.696770668029785,-0.00010007726814365,-0.895998001098633,0.444058060646057,-2.8099566407036e-005,-0.717280209064484,0.696784794330597,0,-9.31275412341392e-009,0,-1,-1.14998333078375e-009,0,-0.999999940395355,-1.24261569922623e-008,0,-1,-0.596549451351166,-0.802576243877411,0,-0.596549451351166,-0.802576303482056,0,-0.596549451351166,-0.802576303482056,0,-0.596549451351166,-0.802576303482056,0,-0.596549451351166,-0.802576303482056,0,-0.596549451351166,-0.802576243877411,0,0.896478891372681,0.443023830652237,-0.00744863739237189,0.895746648311615,0.444499462842941,-0.00763859041035175,0.719893276691437,0.694084346294403,-0.000809962104540318,0.719893276691437,0.694084346294403,-0.000809962104540318,0.895746648311615,0.444499462842941,-0.00763859041035175,0.720004379749298,0.693969428539276,0,0.594018459320068,-0.804451286792755,0,0.594018578529358,-0.8044513463974,0,0.594018578529358,-0.8044513463974,0,0.594018578529358,-0.8044513463974,0,0.594018578529358,-0.8044513463974,0,0.594018459320068,-0.804451286792755,0,1.2744937549769e-008,0,-1,-1.14998333078375e-009,0,-0.999999940395355,1.80098724822386e-009,0,-1,-1.14998333078375e-009,0,-0.999999940395355,-9.31275412341392e-009,0,-1,1.80098724822386e-009,0,-1,-9.31275412341392e-009,0,-1,-6.36990371560842e-009,0,-1,1.80098724822386e-009,0,-1,-0.999987542629242,0.00497916201129556,0,-0.895998001098633,0.444058060646057,-2.8099566407036e-005,-0.999987840652466,0.004921889398247,0.000202789306058548,-0.999987840652466,0.004921889398247,0.000202789306058548,-0.895998001098633,0.444058060646057,-2.8099566407036e-005,-0.896532595157623,0.44297781586647,1.29608179122442e-006,-1.27155370677201e-008,0,1,-5.44747313924177e-009,0,1,-7.57948565022204e-012,0,1,-5.44747313924177e-009,0,1,5.44038503136335e-009,0,0.999999940395355,
-7.57948565022204e-012,0,1,-7.57948565022204e-012,0,1,5.44038503136335e-009,0,0.999999940395355,1.27395196614088e-008,0,1,0.999953806400299,0.00960826128721237,0,0.99994820356369,0.0100561669096351,-0.00159258279018104,0.896478891372681,0.443023830652237,-0.00744863739237189,0.896478891372681,0.443023830652237,-0.00744863739237189,0.99994820356369,0.0100561669096351,-0.00159258279018104,0.895746648311615,0.444499462842941,-0.00763859041035175
			} 
			NormalsW: *60 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *38 {
				a: 0.375,0,0.625,0,0.375,0.25,0.625,0.25,0.375,0.5,0.625,0.5,0.375,0.75,0.625,0.75,0.375,1,0.625,1,0.875,0,0.875,0.25,0.125,0,0.125,0.25,0.625,0.608298778533936,0.875,0.141701251268387,0.517829775810242,0.5,0.517912030220032,0.25,0.625,0.141699254512787
			} 
			UVIndex: *60 {
				a: 17,18,3,16,17,5,5,17,3,14,16,5,6,7,8,8,7,9,18,15,3,3,15,11,12,0,13,13,0,2,4,16,6,16,14,6,14,7,6,2,17,4,4,17,16,1,18,0,18,17,0,0,17,2,1,10,18,18,10,15
			} 
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "AllSame"
			ReferenceInformationType: "IndexToDirect"
			Materials: *1 {
				a: 0
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}
	Model: 2405865933632, "Model::cheveron", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationPivot", "Vector3D", "Vector", "",3.37507799486048e-014,-1.62485291889656,0
			P: "ScalingPivot", "Vector3D", "Vector", "",3.37507799486048e-014,-1.62485291889656,0
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Material: 2405866443952, "Material::cheveronMaterial", "" {
		Version: 102
		ShadingModel: "lambert"
		MultiLayer: 0
		Properties70:  {
			P: "AmbientColor", "Color", "", "A",0,0,0
			P: "DiffuseColor", "Color", "", "A",0.496299684047699,0.496299684047699,0.496299684047699
			P: "DiffuseFactor", "Number", "", "A",0.800000011920929
			P: "TransparencyFactor", "Number", "", "A",1
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "Ambient", "Vector3D", "Vector", "",0,0,0
			P: "Diffuse", "Vector3D", "Vector", "",0.397039753154512,0.397039753154512,0.397039753154512
			P: "Opacity", "double", "Number", "",1
		}
	}
}

; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::cheveron, Model::RootNode
	C: "OO",2405865933632,0
	
	;AnimLayer::BaseLayer, AnimStack::Take 001
	C: "OO",2406263486288,2406159931344
	
	;Geometry::, Model::cheveron
	C: "OO",2405685750912,2405865933632
	
	;Material::cheveronMaterial, Model::cheveron
	C: "OO",2405866443952,2405865933632
}
