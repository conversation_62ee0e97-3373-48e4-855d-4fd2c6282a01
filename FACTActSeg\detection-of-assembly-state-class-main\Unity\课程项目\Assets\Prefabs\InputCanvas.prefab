%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &6827021369646977656
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2844602305280454527}
  - component: {fileID: 32353646183018460}
  - component: {fileID: 3753098136818674548}
  m_Layer: 5
  m_Name: Title
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2844602305280454527
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6827021369646977656}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -3.999939}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4786793626784977411}
  m_Father: {fileID: 8084633427074341655}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 10, y: -19.8}
  m_SizeDelta: {x: 180, y: 19.8}
  m_Pivot: {x: 0, y: 0}
--- !u!114 &32353646183018460
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6827021369646977656}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 30649d3a9faa99c48a7b1166b86bf2a0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Padding:
    m_Left: 10
    m_Right: 10
    m_Top: 0
    m_Bottom: 0
  m_ChildAlignment: 4
  m_Spacing: 0
  m_ChildForceExpandWidth: 1
  m_ChildForceExpandHeight: 1
  m_ChildControlWidth: 1
  m_ChildControlHeight: 1
  m_ChildScaleWidth: 0
  m_ChildScaleHeight: 0
  m_ReverseArrangement: 0
--- !u!222 &3753098136818674548
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6827021369646977656}
  m_CullTransparentMesh: 1
--- !u!1 &7964761545813764014
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1338475611989681799}
  - component: {fileID: 7722100114233455935}
  - component: {fileID: 9167309250651741519}
  - component: {fileID: 6909863207611998132}
  - component: {fileID: 673820127800951319}
  m_Layer: 0
  m_Name: InputCanvas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!224 &1338475611989681799
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7964761545813764014}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -2.5}
  m_LocalScale: {x: 0.0043878625, y: 0.0043878625, z: 0.0043878625}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8084633427074341655}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5002182, y: 0.50017923}
  m_AnchorMax: {x: 0.5002182, y: 0.50017923}
  m_AnchoredPosition: {x: 0, y: 0.6}
  m_SizeDelta: {x: 200, y: 81.8}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &7722100114233455935
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7964761545813764014}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 50cdab0cd5a0916419324bb54314773d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  autoConstraintSelection: 1
  selectedConstraints: []
--- !u!114 &9167309250651741519
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7964761545813764014}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 59f8146938fff824cb5fd77236b75775, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Padding:
    m_Left: 0
    m_Right: 0
    m_Top: 0
    m_Bottom: 0
  m_ChildAlignment: 4
  m_Spacing: 0
  m_ChildForceExpandWidth: 1
  m_ChildForceExpandHeight: 1
  m_ChildControlWidth: 1
  m_ChildControlHeight: 1
  m_ChildScaleWidth: 0
  m_ChildScaleHeight: 0
  m_ReverseArrangement: 0
--- !u!222 &6909863207611998132
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7964761545813764014}
  m_CullTransparentMesh: 1
--- !u!114 &673820127800951319
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7964761545813764014}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3245ec927659c4140ac4f8d17403cc18, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_HorizontalFit: 2
  m_VerticalFit: 2
--- !u!1 &8335951485583244691
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4786793626784977411}
  - component: {fileID: 2542161124334623564}
  - component: {fileID: 7765022242548657831}
  - component: {fileID: 2834572539328384169}
  m_Layer: 5
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &4786793626784977411
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8335951485583244691}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2844602305280454527}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 90, y: -9.9}
  m_SizeDelta: {x: 160, y: 19.8}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &2542161124334623564
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8335951485583244691}
  m_CullTransparentMesh: 1
--- !u!114 &7765022242548657831
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8335951485583244691}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: "\u8BF7\u8F93\u5165\u670D\u52A1\u7AEFIP\u8FDB\u884C\u8FDE\u63A5"
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: b9d84b113be9c9141bd0875ee0e03b4f, type: 2}
  m_sharedMaterial: {fileID: 8910337757693557302, guid: b9d84b113be9c9141bd0875ee0e03b4f, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 10
  m_fontSizeBase: 10
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_enableWordWrapping: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0.21741869, y: 0, z: -0.5429077, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!114 &2834572539328384169
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8335951485583244691}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 306cc8c2b49d7114eaa3623786fc2126, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreLayout: 0
  m_MinWidth: -1
  m_MinHeight: -1
  m_PreferredWidth: -1
  m_PreferredHeight: -1
  m_FlexibleWidth: 1
  m_FlexibleHeight: -1
  m_LayoutPriority: 0
--- !u!1001 &3239103171649400452
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 8084633427074341655}
    m_Modifications:
    - target: {fileID: 1925175511647506998, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_Spacing.x
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 1925175511647506998, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_Spacing.y
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 1925175511647506998, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_ChildAlignment
      value: 4
      objectReference: {fileID: 0}
    - target: {fileID: 1925175511647506998, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_Padding.m_Left
      value: 10
      objectReference: {fileID: 0}
    - target: {fileID: 1925175511647506998, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_ConstraintCount
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1925175511647506998, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_Padding.m_Right
      value: 10
      objectReference: {fileID: 0}
    - target: {fileID: 5299992976367457857, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_text
      value: "\uF36A"
      objectReference: {fileID: 0}
    - target: {fileID: 5299992978129538516, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_text
      value: "\uF295"
      objectReference: {fileID: 0}
    - target: {fileID: 6264478733034656625, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_Enabled
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6264478733034656630, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_Name
      value: ButtonBar
      objectReference: {fileID: 0}
    - target: {fileID: 6264478733034656631, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 6264478733034656631, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 6264478733034656631, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6264478733034656631, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6264478733034656631, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6264478733034656631, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6264478733034656631, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_SizeDelta.x
      value: 134
      objectReference: {fileID: 0}
    - target: {fileID: 6264478733034656631, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_SizeDelta.y
      value: 32
      objectReference: {fileID: 0}
    - target: {fileID: 6264478733034656631, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6264478733034656631, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6264478733034656631, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6264478733034656631, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6264478733034656631, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 6264478733034656631, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 6264478733034656631, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 6264478733034656631, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 100
      objectReference: {fileID: 0}
    - target: {fileID: 6264478733034656631, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -65.8
      objectReference: {fileID: 0}
    - target: {fileID: 6264478733034656631, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6264478733034656631, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6264478733034656631, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7201071552643397555, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_fontAsset
      value: 
      objectReference: {fileID: 11400000, guid: dd9fcdd5e1b94e542b89faa6c9ff3d1d, type: 2}
    - target: {fileID: 7201071552643397555, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_hasFontAssetChanged
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7201071553833970726, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_fontAsset
      value: 
      objectReference: {fileID: 11400000, guid: dd9fcdd5e1b94e542b89faa6c9ff3d1d, type: 2}
    - target: {fileID: 7201071553833970726, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_hasFontAssetChanged
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7718400487773454305, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7718400487773454305, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7718400487773454305, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7718400487773454305, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7718400487773454305, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7718400487773454305, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7718400488100995093, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7718400488100995093, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7718400488100995093, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_SizeDelta.x
      value: 12
      objectReference: {fileID: 0}
    - target: {fileID: 7718400488100995093, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_SizeDelta.y
      value: 31.999989
      objectReference: {fileID: 0}
    - target: {fileID: 7718400488100995093, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 16
      objectReference: {fileID: 0}
    - target: {fileID: 7718400488100995093, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -15.999994
      objectReference: {fileID: 0}
    - target: {fileID: 7718400488766976910, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7718400488766976910, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7718400488766976910, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7718400488766976910, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7718400488766976910, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7718400488766976910, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7718400489292659584, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7718400489292659584, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7718400489292659584, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_SizeDelta.x
      value: 12
      objectReference: {fileID: 0}
    - target: {fileID: 7718400489292659584, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_SizeDelta.y
      value: 31.999989
      objectReference: {fileID: 0}
    - target: {fileID: 7718400489292659584, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 16
      objectReference: {fileID: 0}
    - target: {fileID: 7718400489292659584, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -15.999994
      objectReference: {fileID: 0}
    - target: {fileID: 7761847245326428549, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: currentIconName
      value: Icon 79
      objectReference: {fileID: 0}
    - target: {fileID: 7761847246820078096, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: currentIconName
      value: Icon 57
      objectReference: {fileID: 0}
    - target: {fileID: 8855269297273488366, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_text
      value: "\u786E\u5B9A"
      objectReference: {fileID: 0}
    - target: {fileID: 8855269297273488366, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_fontAsset
      value: 
      objectReference: {fileID: 11400000, guid: b9d84b113be9c9141bd0875ee0e03b4f, type: 2}
    - target: {fileID: 8855269297273488366, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_sharedMaterial
      value: 
      objectReference: {fileID: 8910337757693557302, guid: b9d84b113be9c9141bd0875ee0e03b4f, type: 2}
    - target: {fileID: 8855269297273488366, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_hasFontAssetChanged
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8855269297692579963, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_text
      value: "\u9000\u51FA"
      objectReference: {fileID: 0}
    - target: {fileID: 8855269297692579963, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_fontAsset
      value: 
      objectReference: {fileID: 11400000, guid: b9d84b113be9c9141bd0875ee0e03b4f, type: 2}
    - target: {fileID: 8855269297692579963, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_sharedMaterial
      value: 
      objectReference: {fileID: 8910337757693557302, guid: b9d84b113be9c9141bd0875ee0e03b4f, type: 2}
    - target: {fileID: 8855269297692579963, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_hasFontAssetChanged
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8864547319186590309, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_Name
      value: OK
      objectReference: {fileID: 0}
    - target: {fileID: 8864547319186590330, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8864547319186590330, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8864547319186590330, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_SizeDelta.x
      value: 32
      objectReference: {fileID: 0}
    - target: {fileID: 8864547319186590330, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_SizeDelta.y
      value: 32
      objectReference: {fileID: 0}
    - target: {fileID: 8864547319186590330, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 26
      objectReference: {fileID: 0}
    - target: {fileID: 8864547319186590330, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -16
      objectReference: {fileID: 0}
    - target: {fileID: 8864547319186590333, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: speechRecognitionKeyword
      value: "\u786E\u5B9AIP\u5730\u5740"
      objectReference: {fileID: 0}
    - target: {fileID: 8864547319186590333, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.size
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8864547319186590333, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_LastSelectExited.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 8864547319186590333, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_FirstSelectEntered.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 8864547319186590333, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Mode
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8864547319186590333, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 8864547319186590333, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_MethodName
      value: StartListening
      objectReference: {fileID: 0}
    - target: {fileID: 8864547319186590333, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_TargetAssemblyTypeName
      value: InformationClient, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 8864547319186590333, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 8864547319186590335, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_Size.x
      value: 32
      objectReference: {fileID: 0}
    - target: {fileID: 8864547319186590335, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_Size.y
      value: 32
      objectReference: {fileID: 0}
    - target: {fileID: 8864547319737434737, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_Size.x
      value: 32
      objectReference: {fileID: 0}
    - target: {fileID: 8864547319737434737, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_Size.y
      value: 32
      objectReference: {fileID: 0}
    - target: {fileID: 8864547319737434740, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8864547319737434740, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8864547319737434740, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8864547319737434740, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8864547319737434740, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8864547319737434740, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8864547320873569819, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8864547320873569819, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8864547320873569819, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8864547320873569819, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8864547320873569819, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8864547320873569819, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8864547320873569822, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_Size.x
      value: 32
      objectReference: {fileID: 0}
    - target: {fileID: 8864547320873569822, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_Size.y
      value: 32
      objectReference: {fileID: 0}
    - target: {fileID: 8864547321217110504, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: speechRecognitionKeyword
      value: "\u9000\u51FA\u7A0B\u5E8F"
      objectReference: {fileID: 0}
    - target: {fileID: 8864547321217110504, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.size
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8864547321217110504, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_LastSelectExited.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 8864547321217110504, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_FirstSelectEntered.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 8864547321217110504, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Mode
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8864547321217110504, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 8864547321217110504, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_MethodName
      value: QuitGame
      objectReference: {fileID: 0}
    - target: {fileID: 8864547321217110504, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_TargetAssemblyTypeName
      value: BarController, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 8864547321217110504, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 8864547321217110506, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_Size.x
      value: 32
      objectReference: {fileID: 0}
    - target: {fileID: 8864547321217110506, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_Size.y
      value: 32
      objectReference: {fileID: 0}
    - target: {fileID: 8864547321217110511, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8864547321217110511, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8864547321217110511, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_SizeDelta.x
      value: 32
      objectReference: {fileID: 0}
    - target: {fileID: 8864547321217110511, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_SizeDelta.y
      value: 32
      objectReference: {fileID: 0}
    - target: {fileID: 8864547321217110511, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 108
      objectReference: {fileID: 0}
    - target: {fileID: 8864547321217110511, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -16
      objectReference: {fileID: 0}
    - target: {fileID: 8864547321217110512, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      propertyPath: m_Name
      value: Cancel
      objectReference: {fileID: 0}
    m_RemovedComponents:
    - {fileID: 1925175511647506998, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
    m_RemovedGameObjects:
    - {fileID: 8864547320873569796, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
    - {fileID: 8864547319737434731, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
    m_AddedGameObjects: []
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 6264478733034656630, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
      insertIndex: -1
      addedObject: {fileID: 2768188551017549665}
  m_SourcePrefab: {fileID: 100100000, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
--- !u!1 &8799045330122702322 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 6264478733034656630, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
  m_PrefabInstance: {fileID: 3239103171649400452}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &2768188551017549665
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8799045330122702322}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 30649d3a9faa99c48a7b1166b86bf2a0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Padding:
    m_Left: 10
    m_Right: 10
    m_Top: 0
    m_Bottom: 0
  m_ChildAlignment: 0
  m_Spacing: 50
  m_ChildForceExpandWidth: 1
  m_ChildForceExpandHeight: 1
  m_ChildControlWidth: 0
  m_ChildControlHeight: 0
  m_ChildScaleWidth: 0
  m_ChildScaleHeight: 0
  m_ReverseArrangement: 0
--- !u!224 &8799045330122702323 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 6264478733034656631, guid: c2a4036cbd244524d828230f1bf7c12c, type: 3}
  m_PrefabInstance: {fileID: 3239103171649400452}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &4397182061836287688
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1338475611989681799}
    m_Modifications:
    - target: {fileID: 43583847400634495, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 43583847400634495, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 43583847400634495, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 43583847400634495, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 43583847400634495, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 43583847400634495, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 73387658361364336, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_Size.x
      value: 100
      objectReference: {fileID: 0}
    - target: {fileID: 73387658361364338, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 73387658361364338, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_LastSelectExited.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 73387658361364338, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_FirstSelectEntered.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 73387658361364338, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 73387658361364338, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 73387658361364338, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 73387658361364338, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 73387658361364338, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_MethodName
      value: SetPS
      objectReference: {fileID: 0}
    - target: {fileID: 73387658361364338, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Reload
      objectReference: {fileID: 0}
    - target: {fileID: 73387658361364338, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_TargetAssemblyTypeName
      value: ActionClient, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 73387658361364338, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: CanvasManager, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 73387658361364338, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_IntArgument
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 73387658361364338, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_StringArgument
      value: 1-5
      objectReference: {fileID: 0}
    - target: {fileID: 73387658361364338, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: 1-5
      objectReference: {fileID: 0}
    - target: {fileID: 73387658361364338, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 73387658361364338, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 73387658361364341, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 73387658361364341, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 73387658361364341, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 73387658361364341, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 73387658361364341, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 137906864113613760, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 137906864113613760, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 137906864113613760, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 137906864113613760, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 137906864113613760, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 137906864113613760, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 447971085536269193, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 447971085536269193, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 447971085536269193, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 447971085536269193, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 447971085536269193, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 447971085536269196, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_Size.x
      value: 100
      objectReference: {fileID: 0}
    - target: {fileID: 447971085536269198, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 447971085536269198, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_LastSelectExited.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 447971085536269198, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_FirstSelectEntered.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 447971085536269198, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 447971085536269198, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 447971085536269198, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 447971085536269198, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 447971085536269198, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_MethodName
      value: SetPS
      objectReference: {fileID: 0}
    - target: {fileID: 447971085536269198, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Reload
      objectReference: {fileID: 0}
    - target: {fileID: 447971085536269198, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_TargetAssemblyTypeName
      value: ActionClient, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 447971085536269198, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: CanvasManager, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 447971085536269198, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_IntArgument
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 447971085536269198, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_StringArgument
      value: 1-9
      objectReference: {fileID: 0}
    - target: {fileID: 447971085536269198, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: 1-9
      objectReference: {fileID: 0}
    - target: {fileID: 447971085536269198, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 447971085536269198, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 570267069919180353, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 570267069919180353, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_LastSelectExited.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 570267069919180353, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_FirstSelectEntered.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 570267069919180353, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 570267069919180353, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 570267069919180353, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 570267069919180353, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 570267069919180353, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_MethodName
      value: SetPS
      objectReference: {fileID: 0}
    - target: {fileID: 570267069919180353, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Reload
      objectReference: {fileID: 0}
    - target: {fileID: 570267069919180353, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_TargetAssemblyTypeName
      value: ActionClient, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 570267069919180353, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: CanvasManager, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 570267069919180353, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_IntArgument
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 570267069919180353, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_StringArgument
      value: 2-4
      objectReference: {fileID: 0}
    - target: {fileID: 570267069919180353, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: 2-4
      objectReference: {fileID: 0}
    - target: {fileID: 570267069919180353, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 570267069919180353, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 570267069919180355, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_Size.x
      value: 100
      objectReference: {fileID: 0}
    - target: {fileID: 570267069919180358, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 570267069919180358, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 570267069919180358, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 570267069919180358, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 570267069919180358, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 652184396349140626, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 652184396349140626, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 652184396349140626, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 652184396349140626, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 652184396349140626, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 652184396349140626, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 668015833702402672, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 668015833702402672, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 668015833702402672, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 668015833702402672, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 668015833702402672, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 668015833702402672, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 724632497847713777, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 724632497847713777, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 724632497847713777, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 724632497847713777, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 724632497847713777, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 724632497847713777, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 916243381074383809, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 916243381074383809, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 916243381074383809, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 916243381074383809, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 916243381074383809, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 916243381074383812, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_Size.x
      value: 100
      objectReference: {fileID: 0}
    - target: {fileID: 916243381074383814, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 916243381074383814, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_LastSelectExited.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 916243381074383814, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_FirstSelectEntered.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 916243381074383814, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 916243381074383814, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 916243381074383814, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 916243381074383814, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 916243381074383814, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_MethodName
      value: SetPS
      objectReference: {fileID: 0}
    - target: {fileID: 916243381074383814, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Reload
      objectReference: {fileID: 0}
    - target: {fileID: 916243381074383814, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_TargetAssemblyTypeName
      value: ActionClient, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 916243381074383814, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: CanvasManager, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 916243381074383814, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_IntArgument
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 916243381074383814, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_StringArgument
      value: 1-3
      objectReference: {fileID: 0}
    - target: {fileID: 916243381074383814, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: 1-3
      objectReference: {fileID: 0}
    - target: {fileID: 916243381074383814, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 916243381074383814, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 981670000634822145, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 981670000634822145, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_LastSelectExited.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 981670000634822145, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_FirstSelectEntered.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 981670000634822145, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 981670000634822145, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 981670000634822145, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 981670000634822145, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 981670000634822145, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_MethodName
      value: SetPS
      objectReference: {fileID: 0}
    - target: {fileID: 981670000634822145, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Reload
      objectReference: {fileID: 0}
    - target: {fileID: 981670000634822145, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_TargetAssemblyTypeName
      value: ActionClient, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 981670000634822145, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: CanvasManager, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 981670000634822145, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_IntArgument
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 981670000634822145, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_StringArgument
      value: 1-8
      objectReference: {fileID: 0}
    - target: {fileID: 981670000634822145, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: 1-8
      objectReference: {fileID: 0}
    - target: {fileID: 981670000634822145, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 981670000634822145, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 981670000634822147, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_Size.x
      value: 100
      objectReference: {fileID: 0}
    - target: {fileID: 981670000634822150, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 981670000634822150, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 981670000634822150, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 981670000634822150, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 981670000634822150, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1103443953777792537, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 1103443953777792537, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_LastSelectExited.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 1103443953777792537, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_FirstSelectEntered.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 1103443953777792537, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 1103443953777792537, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 1103443953777792537, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 1103443953777792537, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 1103443953777792537, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_MethodName
      value: SetPS
      objectReference: {fileID: 0}
    - target: {fileID: 1103443953777792537, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Reload
      objectReference: {fileID: 0}
    - target: {fileID: 1103443953777792537, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_TargetAssemblyTypeName
      value: ActionClient, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 1103443953777792537, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: CanvasManager, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 1103443953777792537, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_IntArgument
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1103443953777792537, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_StringArgument
      value: 3-3
      objectReference: {fileID: 0}
    - target: {fileID: 1103443953777792537, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: 3-3
      objectReference: {fileID: 0}
    - target: {fileID: 1103443953777792537, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 1103443953777792537, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 1103443953777792539, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_Size.x
      value: 100
      objectReference: {fileID: 0}
    - target: {fileID: 1103443953777792542, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1103443953777792542, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1103443953777792542, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1103443953777792542, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1103443953777792542, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1149494679802787031, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1149494679802787031, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1149494679802787031, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1149494679802787031, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1149494679802787031, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1149494679802787031, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1233088153048952463, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1233088153048952463, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1233088153048952463, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1233088153048952463, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1233088153048952463, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1233088153048952463, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1598766734351164019, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1598766734351164019, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1598766734351164019, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1598766734351164019, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1598766734351164019, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1598766734351164019, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1725310125689653180, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1725310125689653180, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1725310125689653180, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1725310125689653180, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1725310125689653180, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1725310125689653180, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1807474843324391272, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1807474843324391272, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1807474843324391272, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1807474843324391272, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1807474843324391272, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1807474843324391277, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_Size.x
      value: 100
      objectReference: {fileID: 0}
    - target: {fileID: 1807474843324391279, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 1807474843324391279, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 1807474843324391279, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 1807474843324391279, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 1807474843324391279, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 1807474843324391279, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_MethodName
      value: SetPS
      objectReference: {fileID: 0}
    - target: {fileID: 1807474843324391279, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Reload
      objectReference: {fileID: 0}
    - target: {fileID: 1807474843324391279, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_TargetAssemblyTypeName
      value: ActionClient, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 1807474843324391279, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: CanvasManager, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 1807474843324391279, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_IntArgument
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1807474843324391279, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_StringArgument
      value: 1-1
      objectReference: {fileID: 0}
    - target: {fileID: 1807474843324391279, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: 1-1
      objectReference: {fileID: 0}
    - target: {fileID: 1807474843324391279, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 1807474843324391279, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 1827672374780394378, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1827672374780394378, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1827672374780394378, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1827672374780394378, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1827672374780394378, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1827672374780394381, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 1827672374780394381, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_LastSelectExited.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 1827672374780394381, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_FirstSelectEntered.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 1827672374780394381, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 1827672374780394381, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 1827672374780394381, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 1827672374780394381, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 1827672374780394381, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_MethodName
      value: SetPS
      objectReference: {fileID: 0}
    - target: {fileID: 1827672374780394381, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Reload
      objectReference: {fileID: 0}
    - target: {fileID: 1827672374780394381, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_TargetAssemblyTypeName
      value: ActionClient, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 1827672374780394381, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: CanvasManager, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 1827672374780394381, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_IntArgument
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1827672374780394381, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_StringArgument
      value: 2-0
      objectReference: {fileID: 0}
    - target: {fileID: 1827672374780394381, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: 2-0
      objectReference: {fileID: 0}
    - target: {fileID: 1827672374780394381, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 1827672374780394381, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 1827672374780394383, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_Size.x
      value: 100
      objectReference: {fileID: 0}
    - target: {fileID: 2066958765516825147, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2066958765516825147, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2066958765516825147, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2066958765516825147, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2066958765516825147, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2066958765516825147, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2127916885013008380, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2127916885013008380, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2127916885013008380, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2127916885013008380, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2127916885013008380, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2127916885013008380, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2254138363764615140, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2254138363764615140, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2254138363764615140, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2254138363764615140, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2254138363764615140, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2254138363764615140, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2294500979862376519, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_Enabled
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2294500979862376519, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_Padding.m_Bottom
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2410264314659827486, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2410264314659827486, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2410264314659827486, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2410264314659827486, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2410264314659827486, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2451794779664782886, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2451794779664782886, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2451794779664782886, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2451794779664782886, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2451794779664782886, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2451794779664782886, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2643632575123302529, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2643632575123302529, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2643632575123302529, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2643632575123302529, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2643632575123302529, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2643632575123302529, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2663970775349480547, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2663970775349480547, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2663970775349480547, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2663970775349480547, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2663970775349480547, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2663970775349480547, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2880962788541439232, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2880962788541439232, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2880962788541439232, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2880962788541439232, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2880962788541439232, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2880962788541439232, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3178596312775874865, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3178596312775874865, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3178596312775874865, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3178596312775874865, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3178596312775874865, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3178596312775874865, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3273978707295427214, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3273978707295427214, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3273978707295427214, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3273978707295427214, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3273978707295427214, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3273978707295427214, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3362653852101002827, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_Enabled
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3467258417470558278, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_ChildControlHeight
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3491050777236647181, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3491050777236647181, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3491050777236647181, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3491050777236647181, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3491050777236647181, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3491050777236647181, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3556160376083677386, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3556160376083677386, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3556160376083677386, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3556160376083677386, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3556160376083677386, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3556160376083677386, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3560585422847034964, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_Name
      value: Plate
      objectReference: {fileID: 0}
    - target: {fileID: 3611588758625642457, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_Size.x
      value: 100
      objectReference: {fileID: 0}
    - target: {fileID: 3611588758625642459, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 3611588758625642459, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_LastSelectExited.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 3611588758625642459, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_FirstSelectEntered.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 3611588758625642459, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 3611588758625642459, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 3611588758625642459, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 3611588758625642459, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 3611588758625642459, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_MethodName
      value: SetPS
      objectReference: {fileID: 0}
    - target: {fileID: 3611588758625642459, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Reload
      objectReference: {fileID: 0}
    - target: {fileID: 3611588758625642459, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_TargetAssemblyTypeName
      value: ActionClient, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 3611588758625642459, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: CanvasManager, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 3611588758625642459, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_IntArgument
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3611588758625642459, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_StringArgument
      value: 2-1
      objectReference: {fileID: 0}
    - target: {fileID: 3611588758625642459, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: 2-1
      objectReference: {fileID: 0}
    - target: {fileID: 3611588758625642459, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 3611588758625642459, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 3611588758625642460, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3611588758625642460, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3611588758625642460, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3611588758625642460, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3611588758625642460, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3656109158325683477, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3656109158325683477, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3656109158325683477, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3656109158325683477, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3656109158325683477, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3656109158325683477, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3678458972502042165, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3678458972502042165, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3678458972502042165, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3678458972502042165, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 10.68
      objectReference: {fileID: 0}
    - target: {fileID: 3678458972502042165, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3678458972502042165, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4027144884415556858, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4027144884415556858, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4027144884415556858, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4027144884415556858, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4027144884415556858, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4027144884415556861, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 4027144884415556861, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_LastSelectExited.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 4027144884415556861, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_FirstSelectEntered.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 4027144884415556861, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 4027144884415556861, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 4027144884415556861, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 4027144884415556861, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 4027144884415556861, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_MethodName
      value: SetPS
      objectReference: {fileID: 0}
    - target: {fileID: 4027144884415556861, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Reload
      objectReference: {fileID: 0}
    - target: {fileID: 4027144884415556861, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_TargetAssemblyTypeName
      value: ActionClient, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 4027144884415556861, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: CanvasManager, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 4027144884415556861, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_IntArgument
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4027144884415556861, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_StringArgument
      value: 3-2
      objectReference: {fileID: 0}
    - target: {fileID: 4027144884415556861, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: 3-2
      objectReference: {fileID: 0}
    - target: {fileID: 4027144884415556861, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 4027144884415556861, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 4027144884415556863, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_Size.x
      value: 100
      objectReference: {fileID: 0}
    - target: {fileID: 4180709782481986893, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4180709782481986893, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4180709782481986893, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4180709782481986893, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4180709782481986893, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4180709782481986893, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4270280327461231257, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4270280327461231257, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4270280327461231257, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4270280327461231257, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4270280327461231257, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4312936413935779970, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4312936413935779970, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4312936413935779970, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4312936413935779970, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4312936413935779970, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4312936413935779970, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4324812484920748235, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4324812484920748235, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4324812484920748235, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4324812484920748235, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4324812484920748235, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4324812484920748236, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 4324812484920748236, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_LastSelectExited.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 4324812484920748236, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_FirstSelectEntered.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 4324812484920748236, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 4324812484920748236, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 4324812484920748236, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 4324812484920748236, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 4324812484920748236, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_MethodName
      value: SetPS
      objectReference: {fileID: 0}
    - target: {fileID: 4324812484920748236, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Reload
      objectReference: {fileID: 0}
    - target: {fileID: 4324812484920748236, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_TargetAssemblyTypeName
      value: ActionClient, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 4324812484920748236, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: CanvasManager, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 4324812484920748236, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_IntArgument
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4324812484920748236, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_StringArgument
      value: 2-2
      objectReference: {fileID: 0}
    - target: {fileID: 4324812484920748236, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: 2-2
      objectReference: {fileID: 0}
    - target: {fileID: 4324812484920748236, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 4324812484920748236, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 4324812484920748238, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_Size.x
      value: 100
      objectReference: {fileID: 0}
    - target: {fileID: 4389208820785777790, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4389208820785777790, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4389208820785777790, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4389208820785777790, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4389208820785777790, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4389208820785777790, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4429277874116504433, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_Size.x
      value: 100
      objectReference: {fileID: 0}
    - target: {fileID: 4429277874116504435, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 4429277874116504435, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_LastSelectExited.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 4429277874116504435, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_FirstSelectEntered.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 4429277874116504435, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 4429277874116504435, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 4429277874116504435, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 4429277874116504435, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 4429277874116504435, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_MethodName
      value: SetPS
      objectReference: {fileID: 0}
    - target: {fileID: 4429277874116504435, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Reload
      objectReference: {fileID: 0}
    - target: {fileID: 4429277874116504435, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_TargetAssemblyTypeName
      value: ActionClient, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 4429277874116504435, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: CanvasManager, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 4429277874116504435, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_IntArgument
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4429277874116504435, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_StringArgument
      value: 1-6
      objectReference: {fileID: 0}
    - target: {fileID: 4429277874116504435, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: 1-6
      objectReference: {fileID: 0}
    - target: {fileID: 4429277874116504435, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 4429277874116504435, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 4429277874116504436, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4429277874116504436, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4429277874116504436, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4429277874116504436, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4429277874116504436, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4495185384376261993, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4495185384376261993, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4495185384376261993, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4495185384376261993, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4495185384376261993, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4495185384376261993, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4674443502535295124, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4674443502535295124, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4674443502535295124, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4674443502535295124, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4674443502535295124, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4674443502535295124, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4829204039663894585, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4829204039663894585, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4829204039663894585, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4829204039663894585, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4829204039663894585, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4829204039663894585, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4900553002488766488, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4900553002488766488, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4900553002488766488, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4900553002488766488, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4900553002488766488, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4900553002488766488, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5105236669943673160, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5105236669943673160, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5105236669943673160, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5105236669943673160, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5105236669943673160, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5105236669943673165, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_Size.x
      value: 100
      objectReference: {fileID: 0}
    - target: {fileID: 5105236669943673167, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 5105236669943673167, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_LastSelectExited.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5105236669943673167, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_FirstSelectEntered.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5105236669943673167, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 5105236669943673167, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 5105236669943673167, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 5105236669943673167, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 5105236669943673167, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_MethodName
      value: SetPS
      objectReference: {fileID: 0}
    - target: {fileID: 5105236669943673167, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Reload
      objectReference: {fileID: 0}
    - target: {fileID: 5105236669943673167, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_TargetAssemblyTypeName
      value: ActionClient, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 5105236669943673167, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: CanvasManager, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 5105236669943673167, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_IntArgument
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5105236669943673167, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_StringArgument
      value: 3-1
      objectReference: {fileID: 0}
    - target: {fileID: 5105236669943673167, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: 3-1
      objectReference: {fileID: 0}
    - target: {fileID: 5105236669943673167, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 5105236669943673167, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 5125504053961267698, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5125504053961267698, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5125504053961267698, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5125504053961267698, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5125504053961267698, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5125504053961267698, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5182729035542988336, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5182729035542988336, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5182729035542988336, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5182729035542988336, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5182729035542988336, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5182729035542988336, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5198300756488764666, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5198300756488764666, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5198300756488764666, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5198300756488764666, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5198300756488764666, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5198300756488764666, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5216204584023880488, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_Size.x
      value: 100
      objectReference: {fileID: 0}
    - target: {fileID: 5216204584023880490, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 5216204584023880490, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_LastSelectExited.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5216204584023880490, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_FirstSelectEntered.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5216204584023880490, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 5216204584023880490, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 5216204584023880490, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 5216204584023880490, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 5216204584023880490, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_MethodName
      value: SetPS
      objectReference: {fileID: 0}
    - target: {fileID: 5216204584023880490, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Reload
      objectReference: {fileID: 0}
    - target: {fileID: 5216204584023880490, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_TargetAssemblyTypeName
      value: ActionClient, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 5216204584023880490, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: CanvasManager, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 5216204584023880490, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_IntArgument
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5216204584023880490, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_StringArgument
      value: 1-2
      objectReference: {fileID: 0}
    - target: {fileID: 5216204584023880490, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: 1-2
      objectReference: {fileID: 0}
    - target: {fileID: 5216204584023880490, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 5216204584023880490, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 5216204584023880493, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5216204584023880493, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5216204584023880493, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5216204584023880493, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5216204584023880493, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5343241081339760364, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5343241081339760364, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5343241081339760364, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5343241081339760364, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5343241081339760364, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5343241081339760364, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5356465027970117248, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5356465027970117248, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5356465027970117248, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5356465027970117248, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5356465027970117248, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5356465027970117248, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5380271266876920408, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5380271266876920408, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5380271266876920408, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5380271266876920408, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5380271266876920408, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5380271266876920413, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_Size.x
      value: 100
      objectReference: {fileID: 0}
    - target: {fileID: 5380271266876920415, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 5380271266876920415, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_LastSelectExited.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5380271266876920415, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_FirstSelectEntered.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5380271266876920415, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 5380271266876920415, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 5380271266876920415, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 5380271266876920415, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 5380271266876920415, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_MethodName
      value: SetPS
      objectReference: {fileID: 0}
    - target: {fileID: 5380271266876920415, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Reload
      objectReference: {fileID: 0}
    - target: {fileID: 5380271266876920415, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_TargetAssemblyTypeName
      value: ActionClient, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 5380271266876920415, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: CanvasManager, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 5380271266876920415, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_IntArgument
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5380271266876920415, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_StringArgument
      value: 2-3
      objectReference: {fileID: 0}
    - target: {fileID: 5380271266876920415, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: 2-3
      objectReference: {fileID: 0}
    - target: {fileID: 5380271266876920415, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 5380271266876920415, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 5510192516425288974, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5510192516425288974, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5510192516425288974, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5510192516425288974, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5510192516425288974, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5510192516425288974, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5564072601694463455, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 5564072601694463455, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 5564072601694463455, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5564072601694463455, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5564072601694463455, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5564072601694463455, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5564072601694463455, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 200
      objectReference: {fileID: 0}
    - target: {fileID: 5564072601694463455, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 81.8
      objectReference: {fileID: 0}
    - target: {fileID: 5564072601694463455, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_LocalScale.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5564072601694463455, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_LocalScale.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5564072601694463455, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_LocalScale.z
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5564072601694463455, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5564072601694463455, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5564072601694463455, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_LocalPosition.z
      value: 113
      objectReference: {fileID: 0}
    - target: {fileID: 5564072601694463455, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5564072601694463455, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5564072601694463455, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5564072601694463455, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5564072601694463455, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 100
      objectReference: {fileID: 0}
    - target: {fileID: 5564072601694463455, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -40.9
      objectReference: {fileID: 0}
    - target: {fileID: 5564072601694463455, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5564072601694463455, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5564072601694463455, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5564072601694463455, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5820483684096061801, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 5820483684096061801, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 5820483684096061801, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 5820483684096061801, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 5820483684096061801, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 5820483684096061801, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_MethodName
      value: SetPS
      objectReference: {fileID: 0}
    - target: {fileID: 5820483684096061801, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Reload
      objectReference: {fileID: 0}
    - target: {fileID: 5820483684096061801, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_TargetAssemblyTypeName
      value: ActionClient, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 5820483684096061801, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: CanvasManager, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 5820483684096061801, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_IntArgument
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5820483684096061801, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_StringArgument
      value: 1-0
      objectReference: {fileID: 0}
    - target: {fileID: 5820483684096061801, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: 1-0
      objectReference: {fileID: 0}
    - target: {fileID: 5820483684096061801, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 5820483684096061801, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 5820483684096061803, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_Size.x
      value: 100
      objectReference: {fileID: 0}
    - target: {fileID: 5820483684096061806, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5820483684096061806, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5820483684096061806, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5820483684096061806, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5820483684096061806, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5820483684096061809, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6021927274991730793, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6021927274991730793, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6021927274991730793, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6021927274991730793, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6021927274991730793, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6021927274991730793, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6227908735196248811, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6227908735196248811, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6227908735196248811, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6227908735196248811, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6227908735196248811, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6227908735196248811, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6251285695922766002, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6251285695922766002, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6251285695922766002, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6251285695922766002, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6251285695922766002, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6251285695922766002, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6337890767702063050, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6337890767702063050, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6337890767702063050, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6337890767702063050, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6337890767702063050, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6337890767702063053, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 6337890767702063053, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_LastSelectExited.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 6337890767702063053, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_FirstSelectEntered.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 6337890767702063053, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 6337890767702063053, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 6337890767702063053, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 6337890767702063053, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 6337890767702063053, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_MethodName
      value: SetPS
      objectReference: {fileID: 0}
    - target: {fileID: 6337890767702063053, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Reload
      objectReference: {fileID: 0}
    - target: {fileID: 6337890767702063053, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_TargetAssemblyTypeName
      value: ActionClient, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 6337890767702063053, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: CanvasManager, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 6337890767702063053, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_IntArgument
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6337890767702063053, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_StringArgument
      value: 3-4
      objectReference: {fileID: 0}
    - target: {fileID: 6337890767702063053, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: 3-4
      objectReference: {fileID: 0}
    - target: {fileID: 6337890767702063053, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 6337890767702063053, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 6337890767702063055, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_Size.x
      value: 100
      objectReference: {fileID: 0}
    - target: {fileID: 6375928177046225623, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6375928177046225623, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6375928177046225623, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6375928177046225623, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6375928177046225623, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6375928177046225623, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6507008888599568250, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6507008888599568250, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6507008888599568250, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6507008888599568250, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6507008888599568250, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6507008888599568253, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 6507008888599568253, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_LastSelectExited.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 6507008888599568253, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_FirstSelectEntered.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 6507008888599568253, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 6507008888599568253, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 6507008888599568253, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 6507008888599568253, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 6507008888599568253, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_MethodName
      value: SetPS
      objectReference: {fileID: 0}
    - target: {fileID: 6507008888599568253, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Reload
      objectReference: {fileID: 0}
    - target: {fileID: 6507008888599568253, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_TargetAssemblyTypeName
      value: ActionClient, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 6507008888599568253, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: CanvasManager, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 6507008888599568253, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_IntArgument
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6507008888599568253, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_StringArgument
      value: 1-10
      objectReference: {fileID: 0}
    - target: {fileID: 6507008888599568253, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: 1-10
      objectReference: {fileID: 0}
    - target: {fileID: 6507008888599568253, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 6507008888599568253, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 6507008888599568255, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_Size.x
      value: 100
      objectReference: {fileID: 0}
    - target: {fileID: 6535539712882369442, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6535539712882369442, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6535539712882369442, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6535539712882369442, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6535539712882369442, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6535539712882369442, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6571099898106895703, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_Enabled
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6571099898106895703, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_fontSize
      value: 8
      objectReference: {fileID: 0}
    - target: {fileID: 6571099898106895703, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_fontAsset
      value: 
      objectReference: {fileID: 11400000, guid: b9d84b113be9c9141bd0875ee0e03b4f, type: 2}
    - target: {fileID: 6571099898106895703, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_fontStyle
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6571099898106895703, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_overflowMode
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6571099898106895703, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_sharedMaterial
      value: 
      objectReference: {fileID: 8910337757693557302, guid: b9d84b113be9c9141bd0875ee0e03b4f, type: 2}
    - target: {fileID: 6571099898106895703, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_enableAutoSizing
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6571099898106895703, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_TextStyleHashCode
      value: -1183493901
      objectReference: {fileID: 0}
    - target: {fileID: 6571099898106895703, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_enableWordWrapping
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6571099898106895703, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_overrideHtmlColors
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6571099898106895703, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_hasFontAssetChanged
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6571099898106895703, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_enableVertexGradient
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6656413079086365937, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_Size.x
      value: 100
      objectReference: {fileID: 0}
    - target: {fileID: 6656413079086365939, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 6656413079086365939, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_LastSelectExited.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 6656413079086365939, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_FirstSelectEntered.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 6656413079086365939, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 6656413079086365939, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 6656413079086365939, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 6656413079086365939, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 6656413079086365939, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_MethodName
      value: SetPS
      objectReference: {fileID: 0}
    - target: {fileID: 6656413079086365939, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Reload
      objectReference: {fileID: 0}
    - target: {fileID: 6656413079086365939, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_TargetAssemblyTypeName
      value: ActionClient, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 6656413079086365939, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: CanvasManager, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 6656413079086365939, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_IntArgument
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6656413079086365939, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_StringArgument
      value: 1-12
      objectReference: {fileID: 0}
    - target: {fileID: 6656413079086365939, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: 1-12
      objectReference: {fileID: 0}
    - target: {fileID: 6656413079086365939, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 6656413079086365939, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 6656413079086365940, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6656413079086365940, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6656413079086365940, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6656413079086365940, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6656413079086365940, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7029406435020681215, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7029406435020681215, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7029406435020681215, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7029406435020681215, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7029406435020681215, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7029406435020681215, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7336724370773188107, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7336724370773188107, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7336724370773188107, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7336724370773188107, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7336724370773188107, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7336724370773188107, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7467154681190222961, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7467154681190222961, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7467154681190222961, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7467154681190222961, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7467154681190222961, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7467154681190222961, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7618043018362396527, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7618043018362396527, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7618043018362396527, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7618043018362396527, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7618043018362396527, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7635598027026727657, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7635598027026727657, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7635598027026727657, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7635598027026727657, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7635598027026727657, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7635598027026727657, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7645306321405715649, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7645306321405715649, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7645306321405715649, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7645306321405715649, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7645306321405715649, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7645306321405715649, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7697821409135082243, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7697821409135082243, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7697821409135082243, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7697821409135082243, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7697821409135082243, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7697821409135082243, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7746431576775911904, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7746431576775911904, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7746431576775911904, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7746431576775911904, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7746431576775911904, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7746431576775911909, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_Size.x
      value: 100
      objectReference: {fileID: 0}
    - target: {fileID: 7746431576775911911, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 7746431576775911911, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_LastSelectExited.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 7746431576775911911, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_FirstSelectEntered.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 7746431576775911911, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 7746431576775911911, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 7746431576775911911, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 7746431576775911911, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 7746431576775911911, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_MethodName
      value: SetPS
      objectReference: {fileID: 0}
    - target: {fileID: 7746431576775911911, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Reload
      objectReference: {fileID: 0}
    - target: {fileID: 7746431576775911911, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_TargetAssemblyTypeName
      value: ActionClient, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 7746431576775911911, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: CanvasManager, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 7746431576775911911, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_IntArgument
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7746431576775911911, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_StringArgument
      value: 3-0
      objectReference: {fileID: 0}
    - target: {fileID: 7746431576775911911, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: 3-0
      objectReference: {fileID: 0}
    - target: {fileID: 7746431576775911911, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 7746431576775911911, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 7866557748559012453, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7866557748559012453, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7866557748559012453, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7866557748559012453, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7866557748559012453, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7866557748559012453, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7962097467165119330, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7962097467165119330, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7962097467165119330, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7962097467165119330, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 32
      objectReference: {fileID: 0}
    - target: {fileID: 7962097467165119330, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7962097467165119330, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7962097467165119333, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 7962097467165119333, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 7962097467165119333, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 7962097467165119333, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 7962097467165119333, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 7962097467165119333, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_MethodName
      value: SetPS
      objectReference: {fileID: 0}
    - target: {fileID: 7962097467165119333, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Reload
      objectReference: {fileID: 0}
    - target: {fileID: 7962097467165119333, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_TargetAssemblyTypeName
      value: ActionClient, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 7962097467165119333, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: CanvasManager, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 7962097467165119333, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_IntArgument
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7962097467165119333, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_BoolArgument
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7962097467165119333, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_BoolArgument
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7962097467165119333, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_StringArgument
      value: 0-0
      objectReference: {fileID: 0}
    - target: {fileID: 7962097467165119333, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: 0-0
      objectReference: {fileID: 0}
    - target: {fileID: 7962097467165119333, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 7962097467165119333, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 7962097467165119335, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_Size.x
      value: 100
      objectReference: {fileID: 0}
    - target: {fileID: 7962097467165119335, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_Size.y
      value: 32
      objectReference: {fileID: 0}
    - target: {fileID: 7962097467165119357, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7974523787171474349, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7974523787171474349, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7974523787171474349, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7974523787171474349, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7974523787171474349, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7974523787171474349, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8470863993327951910, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8470863993327951910, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8470863993327951910, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8470863993327951910, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8470863993327951910, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8470863993327951910, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8487369374663733233, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8487369374663733233, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8487369374663733233, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8487369374663733233, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8487369374663733233, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8487369374663733236, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_Size.x
      value: 100
      objectReference: {fileID: 0}
    - target: {fileID: 8487369374663733238, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 8487369374663733238, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_LastSelectExited.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 8487369374663733238, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_FirstSelectEntered.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 8487369374663733238, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 8487369374663733238, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 8487369374663733238, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 8487369374663733238, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 8487369374663733238, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_MethodName
      value: SetPS
      objectReference: {fileID: 0}
    - target: {fileID: 8487369374663733238, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Reload
      objectReference: {fileID: 0}
    - target: {fileID: 8487369374663733238, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_TargetAssemblyTypeName
      value: ActionClient, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 8487369374663733238, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: CanvasManager, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 8487369374663733238, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_IntArgument
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8487369374663733238, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_StringArgument
      value: 1-11
      objectReference: {fileID: 0}
    - target: {fileID: 8487369374663733238, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: 1-11
      objectReference: {fileID: 0}
    - target: {fileID: 8487369374663733238, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 8487369374663733238, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 8595035997337862483, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8595035997337862483, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8595035997337862483, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8595035997337862483, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8595035997337862483, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8595035997337862483, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8786348569002341139, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8786348569002341139, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8786348569002341139, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8786348569002341139, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8786348569002341139, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8786348569002341140, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 8786348569002341140, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_LastSelectExited.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 8786348569002341140, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_FirstSelectEntered.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 8786348569002341140, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 8786348569002341140, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 8786348569002341140, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 8786348569002341140, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 8786348569002341140, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_MethodName
      value: SetPS
      objectReference: {fileID: 0}
    - target: {fileID: 8786348569002341140, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Reload
      objectReference: {fileID: 0}
    - target: {fileID: 8786348569002341140, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_TargetAssemblyTypeName
      value: ActionClient, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 8786348569002341140, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: CanvasManager, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 8786348569002341140, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_IntArgument
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8786348569002341140, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_StringArgument
      value: 1-4
      objectReference: {fileID: 0}
    - target: {fileID: 8786348569002341140, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: 1-4
      objectReference: {fileID: 0}
    - target: {fileID: 8786348569002341140, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 8786348569002341140, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 8786348569002341142, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_Size.x
      value: 100
      objectReference: {fileID: 0}
    - target: {fileID: 8848536714123922169, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8848536714123922169, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8848536714123922169, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8848536714123922169, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8848536714123922169, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8848536714123922172, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_Size.x
      value: 100
      objectReference: {fileID: 0}
    - target: {fileID: 8848536714123922174, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 8848536714123922174, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_LastSelectExited.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 8848536714123922174, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_FirstSelectEntered.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 8848536714123922174, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 8848536714123922174, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 8848536714123922174, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 8848536714123922174, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 8848536714123922174, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_MethodName
      value: SetPS
      objectReference: {fileID: 0}
    - target: {fileID: 8848536714123922174, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Reload
      objectReference: {fileID: 0}
    - target: {fileID: 8848536714123922174, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_TargetAssemblyTypeName
      value: ActionClient, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 8848536714123922174, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: CanvasManager, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 8848536714123922174, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_IntArgument
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8848536714123922174, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_StringArgument
      value: 1-7
      objectReference: {fileID: 0}
    - target: {fileID: 8848536714123922174, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: 1-7
      objectReference: {fileID: 0}
    - target: {fileID: 8848536714123922174, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 8848536714123922174, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: <OnClicked>k__BackingField.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 8877811160460436035, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8877811160460436035, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8877811160460436035, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8877811160460436035, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8877811160460436035, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8877811160460436035, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8906058418140715034, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8906058418140715034, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8906058418140715034, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8906058418140715034, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8906058418140715034, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8906058418140715034, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9108385102195679896, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9108385102195679896, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9108385102195679896, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9108385102195679896, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9108385102195679896, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9108385102195679896, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents:
    - {fileID: 2294500979862376519, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
    m_RemovedGameObjects:
    - {fileID: 2577786310802181881, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
    - {fileID: 5541962486528165467, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
    - {fileID: 1524262174625241621, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
    - {fileID: 800423183685531767, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
    - {fileID: 5692783726039911222, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
    - {fileID: 2164354675983320116, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
    - {fileID: 8932062886892943604, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
    - {fileID: 6918390297274829252, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
    m_AddedGameObjects:
    - targetCorrespondingSourceObject: {fileID: 5564072601694463455, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      insertIndex: -1
      addedObject: {fileID: 2844602305280454527}
    - targetCorrespondingSourceObject: {fileID: 5564072601694463455, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      insertIndex: -1
      addedObject: {fileID: 4655903617109353428}
    - targetCorrespondingSourceObject: {fileID: 5564072601694463455, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      insertIndex: -1
      addedObject: {fileID: 8799045330122702323}
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 3560585422847034964, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
      insertIndex: -1
      addedObject: {fileID: 8340088830130336259}
  m_SourcePrefab: {fileID: 100100000, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
--- !u!1 &895178775317444764 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 3560585422847034964, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
  m_PrefabInstance: {fileID: 4397182061836287688}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &8340088830130336259
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 895178775317444764}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 59f8146938fff824cb5fd77236b75775, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Padding:
    m_Left: 10
    m_Right: 10
    m_Top: 0
    m_Bottom: 0
  m_ChildAlignment: 4
  m_Spacing: 0
  m_ChildForceExpandWidth: 1
  m_ChildForceExpandHeight: 1
  m_ChildControlWidth: 0
  m_ChildControlHeight: 0
  m_ChildScaleWidth: 0
  m_ChildScaleHeight: 0
  m_ReverseArrangement: 0
--- !u!224 &8084633427074341655 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 5564072601694463455, guid: 6315b88c60f80b04bbe6000ed4eedd11, type: 3}
  m_PrefabInstance: {fileID: 4397182061836287688}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &6349080735446094481
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 8084633427074341655}
    m_Modifications:
    - target: {fileID: 43253009, guid: 9039aae1f4c02da49a5d92f4a1c91fd4, type: 3}
      propertyPath: m_Text
      value: ***********
      objectReference: {fileID: 0}
    - target: {fileID: 30129226426062617, guid: 9039aae1f4c02da49a5d92f4a1c91fd4, type: 3}
      propertyPath: m_fontAsset
      value: 
      objectReference: {fileID: 11400000, guid: dd9fcdd5e1b94e542b89faa6c9ff3d1d, type: 2}
    - target: {fileID: 30129226426062617, guid: 9039aae1f4c02da49a5d92f4a1c91fd4, type: 3}
      propertyPath: m_hasFontAssetChanged
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1765799051758416194, guid: 9039aae1f4c02da49a5d92f4a1c91fd4, type: 3}
      propertyPath: speechRecognitionKeyword
      value: "\u8F93\u5165IP\u5730\u5740"
      objectReference: {fileID: 0}
    - target: {fileID: 1765799051758416197, guid: 9039aae1f4c02da49a5d92f4a1c91fd4, type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 1765799051758416197, guid: 9039aae1f4c02da49a5d92f4a1c91fd4, type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 1765799051758416197, guid: 9039aae1f4c02da49a5d92f4a1c91fd4, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1765799051758416197, guid: 9039aae1f4c02da49a5d92f4a1c91fd4, type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1765799051758416197, guid: 9039aae1f4c02da49a5d92f4a1c91fd4, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1765799051758416197, guid: 9039aae1f4c02da49a5d92f4a1c91fd4, type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1765799051758416197, guid: 9039aae1f4c02da49a5d92f4a1c91fd4, type: 3}
      propertyPath: m_SizeDelta.x
      value: 160
      objectReference: {fileID: 0}
    - target: {fileID: 1765799051758416197, guid: 9039aae1f4c02da49a5d92f4a1c91fd4, type: 3}
      propertyPath: m_SizeDelta.y
      value: 30
      objectReference: {fileID: 0}
    - target: {fileID: 1765799051758416197, guid: 9039aae1f4c02da49a5d92f4a1c91fd4, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1765799051758416197, guid: 9039aae1f4c02da49a5d92f4a1c91fd4, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1765799051758416197, guid: 9039aae1f4c02da49a5d92f4a1c91fd4, type: 3}
      propertyPath: m_LocalPosition.z
      value: -0.1
      objectReference: {fileID: 0}
    - target: {fileID: 1765799051758416197, guid: 9039aae1f4c02da49a5d92f4a1c91fd4, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1765799051758416197, guid: 9039aae1f4c02da49a5d92f4a1c91fd4, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1765799051758416197, guid: 9039aae1f4c02da49a5d92f4a1c91fd4, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1765799051758416197, guid: 9039aae1f4c02da49a5d92f4a1c91fd4, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1765799051758416197, guid: 9039aae1f4c02da49a5d92f4a1c91fd4, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 100
      objectReference: {fileID: 0}
    - target: {fileID: 1765799051758416197, guid: 9039aae1f4c02da49a5d92f4a1c91fd4, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -34.8
      objectReference: {fileID: 0}
    - target: {fileID: 1765799051758416197, guid: 9039aae1f4c02da49a5d92f4a1c91fd4, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1765799051758416197, guid: 9039aae1f4c02da49a5d92f4a1c91fd4, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1765799051758416197, guid: 9039aae1f4c02da49a5d92f4a1c91fd4, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1765799051758416218, guid: 9039aae1f4c02da49a5d92f4a1c91fd4, type: 3}
      propertyPath: m_Name
      value: InputField
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 9039aae1f4c02da49a5d92f4a1c91fd4, type: 3}
--- !u!224 &4655903617109353428 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 1765799051758416197, guid: 9039aae1f4c02da49a5d92f4a1c91fd4, type: 3}
  m_PrefabInstance: {fileID: 6349080735446094481}
  m_PrefabAsset: {fileID: 0}
