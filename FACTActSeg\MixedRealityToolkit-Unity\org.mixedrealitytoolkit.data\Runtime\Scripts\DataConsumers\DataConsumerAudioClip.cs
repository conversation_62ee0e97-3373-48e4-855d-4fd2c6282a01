﻿// Copyright (c) Mixed Reality Toolkit Contributors
// Licensed under the BSD 3-Clause

// Disable "missing XML comment" warning for the experimental package.
// While nice to have, documentation is not required for this experimental package.
#pragma warning disable CS1591

using System;
using UnityEngine;

namespace MixedReality.Toolkit.Data
{
    /// <summary>
    /// Given a value from a data source, use that value to look up the correct AudioClip
    /// specified in the Unity inspector list. That AudioClip is then associated
    /// with any AudioClipRenderer being managed by this object.
    /// </summary>
    ///
    /// <remarks>
    /// TODO: Allow for a default AudioClip if no look up can be found.
    /// </remarks>
    [Serializable]
    [AddComponentMenu("MRTK/Data Binding/Consumers/Data Consumer Audio Clip", -10)]
    public class DataConsumerAudioClip : DataConsumerThemableBase<AudioClip>
    {
        /// <inheritdoc/>
        protected override Type[] GetComponentTypes()
        {
            Type[] types = { typeof(AudioSource) };
            return types;
        }

        /// <inheritdoc/>
        protected override void SetObject(Component component, object inValue, AudioClip audioClip)
        {
            AudioSource audioSource = component as AudioSource;

            audioSource.clip = audioClip;
        }
    }
}
#pragma warning restore CS1591